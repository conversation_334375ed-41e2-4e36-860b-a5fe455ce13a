version: "2.0"

only:
  triggerType:
    - push
    - tagPush

parameters:
  imageURL: reg.docker.alibaba-inc.com/linkc/batman-dev:1.24.4-20250606

stages:
  - BUILD
  - RPM
  - CHECK
  - RELEASE

components:
  custom-build:
    kind: agent
    inputs:
      script:
        description: "运行脚本"
    execute:
      agent:
        container:
          image: linkc/batman-dev:1.24.4-20250606
          resourceClass: M
      steps:
        - name: clone
          with:
            singleBranch: true
            depth: 1
            fetchCommitId: true
        - name: shell
          with:
            script: |
              ${{inputs.script}}

jobs:
  build-all:
    stage: BUILD
    component: custom-build
    inputs:
      script: |
        make build

  codefuse-cli-amd64:
    stage: RPM
    id: codefuse-cli-amd64-rpm-build
    component: rpm-build
    inputs:
      image: ${{parameters.imageURL}}
      osVersion: 7
      spec: rpm/codefuse-cli.spec
      rpmName: codefuse-cli
      architecture: x86_64
    only:
      refs:
        - ^build$
        - ^v[0-9][0-9.]*$
  codefuse-cli-aarch64:
    stage: RPM
    id: codefuse-cli-aarch64-rpm-build
    component: rpm-build
    inputs:
      image: ${{parameters.imageURL}}
      osVersion: 7
      spec: rpm/codefuse-cli.spec
      rpmName: codefuse-cli
      architecture: aarch64
    only:
      refs:
        - ^build$
        - ^v[0-9][0-9.]*$

  artifact-transfer-check:
    id: check_artifact
    stage: CHECK
    component: artifact-transfer-check
    inputs:
      artifactsConfigs:
        - artifacts: ${{jobs.codefuse-cli-amd64-rpm-build.outputs.artifacts}}
          antRpmRepoType: current
        - artifacts: ${{jobs.codefuse-cli-aarch64-rpm-build.outputs.artifacts}}
          antRpmRepoType: current
    only:
      refs:
        - ^v[0-9][0-9.]*$

  publish-codefuse-cli:
    stage: RELEASE
    component: ant-artifact-transfer
    inputs:
      transferArtifacts: ${{jobs.check_artifact.outputs.transferArtifacts}}
    config:
      beforeExecute:
        isAutoSkip: ${{jobs.check_artifact.outputs.selectedCount}} = 0
        confirm:
          buttonName: 确认发布正式库
          approvers:
            - yongli.zzp
    only:
      triggerType:
        - tagPush
      refs:
        - ^v[0-9][0-9.]*$