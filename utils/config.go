package utils

import (
	"fmt"
	"os"
)

// BaseURL 统一的服务基础URL
const BaseURL = "https://codexmuse-pre.alipay.com"

// Config 应用配置结构
type Config struct {
	// AI模型配置（支持环境变量）
	AI AIConfig `json:"ai"`

	// 远程检索配置
	RemoteRetrieval RemoteRetrievalConfig `json:"remote_retrieval"`

	// 符号检索配置
	SymbolRetrieval SymbolRetrievalConfig `json:"symbol_retrieval"`

	// 索引构建配置
	IndexBuild IndexBuildConfig `json:"index_build"`
}

// AIConfig AI模型相关配置
type AIConfig struct {
	Model   string `json:"model"`
	APIKey  string `json:"api_key"`
	BaseURL string `json:"base_url"`
}

// RemoteRetrievalConfig 远程检索配置
type RemoteRetrievalConfig struct {
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers"`
}

// SymbolRetrievalConfig 符号检索配置
type SymbolRetrievalConfig struct {
	BaseURL string `json:"base_url"`
}

// IndexBuildConfig 索引构建配置
type IndexBuildConfig struct {
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		AI: AIConfig{
			Model:   "aistudio/DeepSeek-V3",
			APIKey:  "",
			BaseURL: BaseURL + "/v1",
		},
		RemoteRetrieval: RemoteRetrievalConfig{
			URL: BaseURL + "/v1/container/tool/search",
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
		},
		SymbolRetrieval: SymbolRetrievalConfig{
			BaseURL: BaseURL + "/v1/container/tool/symbol",
		},
		IndexBuild: IndexBuildConfig{
			URL: BaseURL + "/v1/container/index/build",
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
		},
	}
}

// LoadConfig 加载配置，优先级：环境变量 > 默认值
func LoadConfig() (*Config, error) {
	config := DefaultConfig()

	// 环境变量覆盖AI配置
	//loadAIConfigFromEnv(config)

	// 环境变量覆盖RemoteRetrieval配置
	loadRemoteRetrievalConfigFromEnv(config)

	// 环境变量覆盖SymbolRetrieval配置（如果需要的话）
	loadSymbolRetrievalConfigFromEnv(config)

	// 环境变量覆盖IndexBuild配置
	loadIndexBuildConfigFromEnv(config)

	return config, nil
}

// loadAIConfigFromEnv 从环境变量加载AI配置
func loadAIConfigFromEnv(config *Config) {
	if model := os.Getenv("CODEFUSE_MODEL"); model != "" {
		config.AI.Model = model
		fmt.Printf("Using model from environment: %s\n", model)
	}

	if apiKey := os.Getenv("CODEFUSE_API_KEY"); apiKey != "" {
		config.AI.APIKey = apiKey
		fmt.Printf("Using API key from environment\n")
	}

	if baseURL := os.Getenv("CODEFUSE_BASE_URL"); baseURL != "" {
		config.AI.BaseURL = baseURL
		fmt.Printf("Using base URL from environment: %s\n", baseURL)
	}

	// 兼容OpenAI环境变量
	if apiKey := os.Getenv("OPENAI_API_KEY"); apiKey != "" && config.AI.APIKey == DefaultConfig().AI.APIKey {
		config.AI.APIKey = apiKey
		fmt.Printf("Using OpenAI API key from environment\n")
	}

	if baseURL := os.Getenv("OPENAI_BASE_URL"); baseURL != "" && config.AI.BaseURL == DefaultConfig().AI.BaseURL {
		config.AI.BaseURL = baseURL
		fmt.Printf("Using OpenAI base URL from environment: %s\n", baseURL)
	}
}

// loadRemoteRetrievalConfigFromEnv 从环境变量加载RemoteRetrieval配置
func loadRemoteRetrievalConfigFromEnv(config *Config) {
	// 新的检索接口不需要鉴权，但可以从环境变量覆盖URL
	if url := os.Getenv("CODEFUSE_REMOTE_RETRIEVAL_URL"); url != "" {
		config.RemoteRetrieval.URL = url
		fmt.Printf("Using remote retrieval URL from environment: %s\n", url)
	}
}

// loadSymbolRetrievalConfigFromEnv 从环境变量加载SymbolRetrieval配置
func loadSymbolRetrievalConfigFromEnv(config *Config) {
	// 新的符号检索接口不需要鉴权，但可以从环境变量覆盖URL
	if baseURL := os.Getenv("CODEFUSE_SYMBOL_BASE_URL"); baseURL != "" {
		config.SymbolRetrieval.BaseURL = baseURL
		fmt.Printf("Using symbol base URL from environment: %s\n", baseURL)
	}
}

// loadIndexBuildConfigFromEnv 从环境变量加载IndexBuild配置
func loadIndexBuildConfigFromEnv(config *Config) {
	if url := os.Getenv("CODEFUSE_INDEX_BUILD_URL"); url != "" {
		config.IndexBuild.URL = url
		fmt.Printf("Using index build URL from environment: %s\n", url)
	}
}

// LoadIndexBuildConfig 专门用于索引构建的配置加载，返回URL和Headers
func LoadIndexBuildConfig() (string, map[string]string) {
	// 默认配置
	url := BaseURL + "/v1/container/index/build"
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	// 从环境变量覆盖URL
	if envURL := os.Getenv("CODEFUSE_INDEX_BUILD_URL"); envURL != "" {
		url = envURL
	}

	return url, headers
}
