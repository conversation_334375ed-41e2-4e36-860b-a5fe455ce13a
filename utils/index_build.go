package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// IndexBuildRequest represents the request structure for index building
type IndexBuildRequest struct {
	RepoURL  string `json:"repoURL"`
	Branch   string `json:"branch"`
	TaskName string `json:"taskName"`
}

// IndexBuildResponse represents the response structure for index building
type IndexBuildResponse struct {
	Success   bool   `json:"success"`
	ErrorCode string `json:"errorCode"`
	ErrorMsg  string `json:"errorMsg"`
	Data      struct {
		State  string `json:"state"`
		TaskId int    `json:"taskId"`
	} `json:"data"`
	TraceId string `json:"traceId"`
}

// EnsureIndexExists ensures that the remote index exists for the given repository and branch
func EnsureIndexExists(repoURL, branch string) bool {
	// 从配置加载器获取索引构建配置
	url, headers := LoadIndexBuildConfig()

	fmt.Printf("🔧 Index Build Configuration:\n")
	fmt.Printf("  URL: %s\n", url)
	fmt.Printf("  Headers: %d configured\n", len(headers))
	fmt.Printf("  Original Repo URL: %s\n", repoURL)
	fmt.Printf("  Branch: %s\n", branch)

	// Convert git SSH URL to HTTP URL
	if strings.HasPrefix(repoURL, "*******************:") {
		originalURL := repoURL
		repoURL = strings.Replace(repoURL, "*******************:", "http://code.alipay.com/", 1)
		fmt.Printf("  Converted URL: %s -> %s\n", originalURL, repoURL)
	}

	payload := IndexBuildRequest{
		RepoURL:  repoURL,
		Branch:   branch,
		TaskName: "REPO_BUILD_V1",
	}

	log.Printf("🔍 Ensuring index exists for %s branch %s", repoURL, branch)

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Marshal payload to JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.Printf("❌ Failed to marshal payload: %v", err)
		return false
	}

	log.Printf("📋 Request payload: %s", string(payloadBytes))

	// Create request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.Printf("❌ Failed to create request: %v", err)
		return false
	}

	// Set headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Initial request to start the build
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ Failed to send initial request: %v", err)
		return false
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ Failed to read response body: %v", err)
		return false
	}

	var response IndexBuildResponse
	if err := json.Unmarshal(body, &response); err != nil {
		log.Printf("❌ Failed to unmarshal response: %v", err)
		return false
	}

	if !response.Success || response.ErrorCode != "SUCCESS" {
		log.Printf("❌ Failed to initiate index build: %s", response.ErrorMsg)
		return false
	}

	// Poll until the build is complete
	maxAttempts := 50
	attempts := 0
	pollInterval := 10 * time.Second

	log.Printf("⏳ Polling index build status...")

	for attempts < maxAttempts {
		log.Printf("🔄 Checking index build status (attempt %d/%d)", attempts+1, maxAttempts)

		// Create new request for polling
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadBytes))
		if err != nil {
			log.Printf("❌ Failed to create polling request: %v", err)
			return false
		}

		// Set headers
		for key, value := range headers {
			req.Header.Set(key, value)
		}

		// Poll the same endpoint to check status
		resp, err := client.Do(req)
		if err != nil {
			log.Printf("❌ Failed to send polling request: %v", err)
			time.Sleep(pollInterval)
			attempts++
			continue
		}

		// Read response
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			log.Printf("❌ Failed to read polling response body: %v", err)
			time.Sleep(pollInterval)
			attempts++
			continue
		}

		var response IndexBuildResponse
		if err := json.Unmarshal(body, &response); err != nil {
			log.Printf("❌ Failed to unmarshal polling response: %v", err)
			time.Sleep(pollInterval)
			attempts++
			continue
		}

		state := response.Data.State
		if state == "SUCCESS" {
			log.Printf("✅ Index build completed successfully")
			return true
		} else {
			log.Printf("⏳ Index build is in progress, state: %s", state)
		}

		attempts++
		time.Sleep(pollInterval)
	}

	log.Printf("❌ Index build did not complete within the timeout period")
	return false
}