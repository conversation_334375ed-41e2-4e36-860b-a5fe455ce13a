package utils

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/go-git/go-git/v5"
)

// GitInfo holds git repository information
type GitInfo struct {
	RemoteURL string
	Branch    string
	CommitID  string
}

// GetGitInfo retrieves git repository information from the current directory
func GetGitInfo() (*GitInfo, error) {
	gitInfo := &GitInfo{
		RemoteURL: "unknown",
		Branch:    "unknown",
		CommitID:  "unknown",
	}

	// Open the repository from current directory
	repo, err := git.PlainOpen(".")
	if err != nil {
		return gitInfo, nil // Return default values if not a git repo
	}

	// Get remote URL
	remotes, err := repo.Remotes()
	if err == nil && len(remotes) > 0 {
		// Priority 1: Look for code.alipay.com remotes first
		for _, remote := range remotes {
			if len(remote.Config().URLs) > 0 {
				for _, url := range remote.Config().URLs {
					if strings.Contains(url, "code.alipay.com") {
						gitInfo.RemoteURL = url
						goto remoteFound
					}
				}
			}
		}
		
		// Priority 2: Look for origin remote
		for _, remote := range remotes {
			if remote.Config().Name == "origin" {
				if len(remote.Config().URLs) > 0 {
					gitInfo.RemoteURL = remote.Config().URLs[0]
					goto remoteFound
				}
			}
		}
		
		// Priority 3: Use first remote if no origin found
		if len(remotes[0].Config().URLs) > 0 {
			gitInfo.RemoteURL = remotes[0].Config().URLs[0]
		}
		
		remoteFound:
	}

	// Get current branch
	head, err := repo.Head()
	if err == nil {
		if head.Name().IsBranch() {
			gitInfo.Branch = head.Name().Short()
		}
		gitInfo.CommitID = head.Hash().String()
	}

	return gitInfo, nil
}

// IsGitRepository checks if the current directory is a git repository
func IsGitRepository() bool {
	_, err := git.PlainOpen(".")
	return err == nil
}

// FileDetail represents file change information
type FileDetail struct {
	FileName       string `json:"fileName"`
	FilePath       string `json:"filePath"`
	FileContent    string `json:"fileContent"`
	FileChangeType string `json:"fileChangeType"` // A: added, M: modified, D: deleted
}

// DiffInfo holds repository diff information
type DiffInfo struct {
	DiffStatus  string       `json:"diffStatus"`  // SUCCESS, ERROR, Empty
	Message     string       `json:"message"`     // Error message when DiffStatus is ERROR
	FileDetails []FileDetail `json:"fileDetails"`
}

// GetDiffInfo retrieves diff information from the current git repository
func GetDiffInfo() (*DiffInfo, error) {
	diffInfo := &DiffInfo{
		FileDetails: make([]FileDetail, 0),
	}

	// Check if it's a git repository
	if !IsGitRepository() {
		diffInfo.DiffStatus = "ERROR"
		diffInfo.Message = "Not a git repository"
		return diffInfo, nil
	}

	// Open the repository
	repo, err := git.PlainOpen(".")
	if err != nil {
		diffInfo.DiffStatus = "ERROR"
		diffInfo.Message = "Failed to open git repository: " + err.Error()
		return diffInfo, nil
	}

	// Get the working tree
	worktree, err := repo.Worktree()
	if err != nil {
		diffInfo.DiffStatus = "ERROR"
		diffInfo.Message = "Failed to get working tree: " + err.Error()
		return diffInfo, nil
	}

	// Get the status
	status, err := worktree.Status()
	if err != nil {
		diffInfo.DiffStatus = "ERROR"
		diffInfo.Message = "Failed to get git status: " + err.Error()
		return diffInfo, nil
	}

	// If no changes, return empty
	if status.IsClean() {
		diffInfo.DiffStatus = "Empty"
		return diffInfo, nil
	}

	// Process each file change
	for filePath, fileStatus := range status {
		// Extract file name from path
		fileName := filepath.Base(filePath)

		fileDetail := FileDetail{
			FileName: fileName,
			FilePath: filePath,
		}

		// Determine change type based on git status
		switch {
		case fileStatus.Staging == git.Added || fileStatus.Worktree == git.Untracked:
			fileDetail.FileChangeType = "A" // Added
		case fileStatus.Staging == git.Modified || fileStatus.Worktree == git.Modified:
			fileDetail.FileChangeType = "M" // Modified
		case fileStatus.Staging == git.Deleted || fileStatus.Worktree == git.Deleted:
			fileDetail.FileChangeType = "D" // Deleted
		default:
			fileDetail.FileChangeType = "M" // Default to modified
		}

		// Get file content (only for non-deleted files)
		if fileDetail.FileChangeType != "D" {
			content, err := getFileContent(filePath)
			if err == nil {
				fileDetail.FileContent = content
			}
		}

		diffInfo.FileDetails = append(diffInfo.FileDetails, fileDetail)
	}

	diffInfo.DiffStatus = "SUCCESS"
	return diffInfo, nil
}

// getFileContent reads the content of a file
func getFileContent(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), nil
} 