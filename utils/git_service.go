package utils

import (
	"log"
	"strings"
	"sync"
)

// GitService 中心化的Git信息服务
type GitService struct {
	mu      sync.RWMutex
	gitInfo *GitInfo
	cached  bool
}

var (
	globalGitService *GitService
	gitServiceOnce   sync.Once
)

// GetGlobalGitService 获取全局Git服务实例（单例模式）
func GetGlobalGitService() *GitService {
	gitServiceOnce.Do(func() {
		globalGitService = &GitService{
			gitInfo: &GitInfo{
				RemoteURL: "unknown",
				Branch:    "unknown",
				CommitID:  "unknown",
			},
			cached: false,
		}
	})
	return globalGitService
}

// GetGitInfo 获取Git信息（带缓存）
func (gs *GitService) GetGitInfo() *GitInfo {
	gs.mu.RLock()
	if gs.cached {
		// 返回缓存的副本，避免外部修改
		gitInfo := *gs.gitInfo
		gs.mu.RUnlock()
		return &gitInfo
	}
	gs.mu.RUnlock()

	// 如果没有缓存，则获取并缓存
	return gs.RefreshGitInfo()
}

// RefreshGitInfo 刷新Git信息
func (gs *GitService) RefreshGitInfo() *GitInfo {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	// 重新获取Git信息
	gitInfo, err := GetGitInfo()
	if err != nil {
		log.Printf("Warning: Failed to get git info: %v", err)
		// 保持原有的默认值
		if gs.gitInfo == nil {
			gs.gitInfo = &GitInfo{
				RemoteURL: "unknown",
				Branch:    "unknown",
				CommitID:  "unknown",
			}
		}
	} else {
		// 统一处理git URL转换 - 将git SSH URL转换为HTTP URL供模型使用
		if gitInfo.RemoteURL != "unknown" && strings.HasPrefix(gitInfo.RemoteURL, "*******************:") {
			gitInfo.RemoteURL = strings.Replace(gitInfo.RemoteURL, "*******************:", "http://code.alipay.com/", 1)
		}
		gs.gitInfo = gitInfo
		gs.cached = true
		
		log.Printf("🔄 Git info refreshed - URL: %s, Branch: %s, Commit: %s",
			gitInfo.RemoteURL, gitInfo.Branch, gitInfo.CommitID)
	}

	// 返回副本，避免外部修改
	result := *gs.gitInfo
	return &result
}

// GetRemoteURL 获取远程URL
func (gs *GitService) GetRemoteURL() string {
	return gs.GetGitInfo().RemoteURL
}

// GetBranch 获取分支名
func (gs *GitService) GetBranch() string {
	return gs.GetGitInfo().Branch
}

// GetCommitID 获取提交ID
func (gs *GitService) GetCommitID() string {
	return gs.GetGitInfo().CommitID
}

// IsGitRepository 检查是否为Git仓库
func (gs *GitService) IsGitRepository() bool {
	gitInfo := gs.GetGitInfo()
	return gitInfo.RemoteURL != "unknown" || gitInfo.Branch != "unknown" || gitInfo.CommitID != "unknown"
}

// InvalidateCache 使缓存失效，下次获取时会重新读取
func (gs *GitService) InvalidateCache() {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	gs.cached = false
	log.Printf("🗑️ Git info cache invalidated")
}

// SetGitInfo 手动设置Git信息（主要用于测试）
func (gs *GitService) SetGitInfo(gitInfo *GitInfo) {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	// 应用URL转换逻辑
	if gitInfo.RemoteURL != "unknown" && strings.HasPrefix(gitInfo.RemoteURL, "*******************:") {
		gitInfo.RemoteURL = strings.Replace(gitInfo.RemoteURL, "*******************:", "http://code.alipay.com/", 1)
	}
	
	gs.gitInfo = gitInfo
	gs.cached = true
	log.Printf("📝 Git info manually set - URL: %s, Branch: %s, Commit: %s",
		gitInfo.RemoteURL, gitInfo.Branch, gitInfo.CommitID)
}

// GetGitInfoCopy 获取Git信息的副本（便于外部使用）
func (gs *GitService) GetGitInfoCopy() GitInfo {
	gitInfo := gs.GetGitInfo()
	return *gitInfo
}
