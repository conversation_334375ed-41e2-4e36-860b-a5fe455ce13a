package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"codefuse-cli/utils"
	"github.com/sashabaranov/go-openai"
)

// runInteractiveChat 运行交互模式
func runInteractiveChat() {
	// 加载配置
	config, err := utils.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 获取当前工作目录作为工作空间根目录
	workspaceRoot, err := os.Getwd()
	if err != nil {
		fmt.Printf("Failed to get current directory: %v\n", err)
		os.Exit(1)
	}

	chatService := NewChatService(config, workspaceRoot)
	
	// 检查是否有输入文件参数
	inputFile := getInputFileFromArgs()
	var fileContent string
	
	if inputFile != "" {
		// 从文件读取整个内容作为用户需求
		content, err := os.ReadFile(inputFile)
		if err != nil {
			fmt.Printf("Failed to open input file %s: %v\n", inputFile, err)
			os.Exit(1)
		}
		fileContent = strings.TrimSpace(string(content))
		if fileContent == "" {
			fmt.Printf("Input file %s is empty\n", inputFile)
			os.Exit(1)
		}
		fmt.Printf("📄 Reading input from file: %s\n", inputFile)
	}

	// 初始化系统 prompt
	systemPrompt, err := chatService.promptManager.GetSystemPrompt()
	if err != nil {
		log.Printf("Warning: Failed to get system prompt: %v", err)
		systemPrompt = "You are a helpful AI assistant."
	}

	supervisionPrompt, err := chatService.promptManager.GetSupervisionPrompt()
	if err != nil {
		log.Printf("Warning: Failed to get supervision prompt: %v", err)
		supervisionPrompt = ""
	}

	// 初始化消息历史，包含系统 prompt
	var messages []openai.ChatCompletionMessage
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: systemPrompt,
	})

	// 如果有 supervision prompt，添加为用户消息
	if supervisionPrompt != "" {
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: supervisionPrompt,
		})
	}

	fmt.Println("🤖 AI Partner (A Par) - DeepSeek Streaming Chat Tool")
	if inputFile == "" {
		fmt.Println("Type 'quit' or 'exit' to end the conversation")
	}
	fmt.Println()

	// 从中心化的Git服务获取Git信息用于显示
	gitService := utils.GetGlobalGitService()
	gitInfo := gitService.GetGitInfo()

	fmt.Println("📋 Repository Information:")
	fmt.Printf("  Git URL: %s\n", gitInfo.RemoteURL)
	fmt.Printf("  Branch: %s\n", gitInfo.Branch)
	fmt.Printf("  Commit: %s\n", gitInfo.CommitID)
	fmt.Println()

	// 打印可用的工具详细信息
	chatService.printDetailedToolsInfo()

	fmt.Printf("📁 Workspace: %s\n", workspaceRoot)
	fmt.Println("----------------------------------------")

	// 确保远程索引存在（如果启用了远程检索功能）
	chatService.ensureRemoteIndexIfNeeded()

	fmt.Println("----------------------------------------")

	for {
		var input string
		
		if inputFile != "" {
			// 文件模式：使用预读取的内容
			input = fileContent
		} else {
			// 交互模式：从标准输入读取
			fmt.Print("\nYou: ")
			scanner := bufio.NewScanner(os.Stdin)
			if !scanner.Scan() {
				break
			}
			input = strings.TrimSpace(scanner.Text())
			if input == "" {
				continue
			}
			if input == "quit" || input == "exit" {
				fmt.Println("Goodbye! 👋")
				break
			}
		}

		fmt.Print("\nYou request: " + input)

		// 添加用户消息
		userMessage := openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: input,
		}
		messages = append(messages, userMessage)

		// 流式聊天
		ctx := context.Background()
		updatedMessages, err := chatService.streamChat(ctx, messages)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			if inputFile != "" {
				break // 文件模式下出错就退出
			}
			continue
		}
		
		// 使用更新后的消息历史，保持对话上下文
		messages = updatedMessages
		
		// 如果是文件输入模式，处理完后退出
		if inputFile != "" {
			break
		}
	}

	if inputFile == "" {
		scanner := bufio.NewScanner(os.Stdin)
		if err := scanner.Err(); err != nil {
			fmt.Printf("Scanner error: %v\n", err)
		}
	}
}

// getInputFileFromArgs 从命令行参数中获取输入文件路径
func getInputFileFromArgs() string {
	for _, arg := range os.Args[1:] {
		if strings.HasPrefix(arg, "--input=") {
			return strings.TrimPrefix(arg, "--input=")
		}
	}
	return ""
}
