# Makefile for codefuse-cli

# Default values
BUILD_TARGET ?= linux
BUILD_ARCH ?= amd64

# Binary name
BINARY_NAME := codefuse-cli

# Build directory
BUILD_DIR := bin

# Go build flags
LDFLAGS := -s -w

# Default target
.PHONY: all
all: build

# Build target
.PHONY: build
build:
	@echo "Building $(BINARY_NAME) for $(BUILD_TARGET)/$(BUILD_ARCH)..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(BUILD_TARGET) GOARCH=$(BUILD_ARCH) go build -ldflags "$(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME) .

# Clean target
.PHONY: clean
clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)

# Install target
.PHONY: install
install: build
	@echo "Installing $(BINARY_NAME)..."
	@install -m 755 $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/

# Test target
.PHONY: test
test:
	@echo "Running tests..."
	@go test ./...

# Format target
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Vet target
.PHONY: vet
vet:
	@echo "Running go vet..."
	@go vet ./...

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build     - Build the binary (default)"
	@echo "  clean     - Clean build directory"
	@echo "  install   - Install binary to /usr/local/bin"
	@echo "  test      - Run tests"
	@echo "  fmt       - Format code"
	@echo "  vet       - Run go vet"
	@echo "  help      - Show this help"
	@echo ""
	@echo "Build variables:"
	@echo "  BUILD_TARGET  - Target OS (default: linux)"
	@echo "  BUILD_ARCH    - Target architecture (default: amd64)"
	@echo ""
	@echo "Examples:"
	@echo "  make build"
	@echo "  make BUILD_TARGET=linux BUILD_ARCH=arm64 build"
	@echo "  make BUILD_TARGET=darwin BUILD_ARCH=amd64 build" 