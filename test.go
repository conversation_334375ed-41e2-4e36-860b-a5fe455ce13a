package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"codefuse-cli/tools"
	"codefuse-cli/utils"
)



func testRemoteRetriever() {
	fmt.Println("🧪 Testing Remote Retriever Tool...")

	// 获取provider manager
	providerManager := GetGlobalProviderManager()
	
	// 使用augment provider进行测试
	config, err := utils.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 获取当前工作目录
	workspaceRoot, err := os.Getwd()
	if err != nil {
		log.Fatalf("Failed to get current directory: %v", err)
	}

	// 获取 Git 信息
	gitInfo, err := utils.GetGitInfo()
	if err != nil {
		log.Printf("Warning: Failed to get git info: %v", err)
		gitInfo = &utils.GitInfo{
			RemoteURL: "unknown",
			Branch:    "unknown",
			CommitID:  "unknown",
		}
	}

	providerConfig := &tools.ProviderConfig{
		WorkspaceRoot: workspaceRoot,
		Config:        config,
		GitInfo:       gitInfo,
	}

	// 创建tool manager
	toolManager, err := providerManager.CreateToolManager("augment", providerConfig)
	if err != nil {
		log.Fatalf("Failed to create tool manager: %v", err)
	}

	// 测试参数
	query := "用户认证相关代码"
	repoURL := "https://code.alipay.com/common_release/codegencore.git"
	branch := "apar_agent_v2_7468ad2cd78dfc3360633d37422d3c2186632f84"

	fmt.Printf("📋 Test Parameters:\n")
	fmt.Printf("  Query: %s\n", query)
	fmt.Printf("  Repo URL: %s\n", repoURL)
	fmt.Printf("  Branch: %s\n", branch)
	fmt.Println()

	fmt.Println("✅ Testing remote retriever through tool manager...")

	// 通过tool manager测试remote_retriever工具
	if !toolManager.HasTool("remote_retriever") {
		fmt.Println("❌ remote_retriever tool not available in current provider")
		return
	}

	// 构建参数
	args := map[string]interface{}{
		"query":    query,
		"repo_url": repoURL,
		"branch":   branch,
	}
	
	argsJSON, err := json.Marshal(args)
	if err != nil {
		fmt.Printf("❌ Failed to marshal arguments: %v\n", err)
		return
	}

	result, err := toolManager.ExecuteTool("remote_retriever", string(argsJSON))
	if err != nil {
		fmt.Printf("❌ Retrieval failed: %v\n", err)
		return
	}

	fmt.Println("✅ Retrieval completed successfully!")
	fmt.Println("📄 Results:")
	fmt.Println("----------------------------------------")
	fmt.Println(result)
	fmt.Println("----------------------------------------")
	fmt.Printf("📊 Result length: %d characters\n", len(result))
}



func testPromptSystem() {
	fmt.Println("🧪 Testing Prompt System and Git Integration...")
	fmt.Println()

	// 获取当前工作目录
	workspaceRoot, err := os.Getwd()
	if err != nil {
		log.Fatalf("Failed to get current directory: %v", err)
	}

	// 获取 Git 信息
	gitInfo, err := utils.GetGitInfo()
	if err != nil {
		log.Printf("Warning: Failed to get git info: %v", err)
		gitInfo = &utils.GitInfo{
			RemoteURL: "unknown",
			Branch:    "unknown",
			CommitID:  "unknown",
		}
	}

	fmt.Println("📋 Git Information:")
	fmt.Printf("  Remote URL: %s\n", gitInfo.RemoteURL)
	fmt.Printf("  Branch: %s\n", gitInfo.Branch)
	fmt.Printf("  Commit ID: %s\n", gitInfo.CommitID)
	fmt.Println()

	// 获取provider manager
	providerManager := GetGlobalProviderManager()
	
	// 创建provider配置
	config, err := utils.LoadConfig()
	if err != nil {
		log.Printf("Warning: Failed to load config: %v", err)
		config = utils.DefaultConfig()
	}

	providerConfig := &tools.ProviderConfig{
		WorkspaceRoot: workspaceRoot,
		Config:        config,
		GitInfo:       gitInfo,
	}

	// 创建 Prompt 管理器
	promptManager, err := providerManager.CreatePromptManager("augment", providerConfig)
	if err != nil {
		log.Fatalf("Failed to create prompt manager: %v", err)
	}

	// 测试系统 Prompt
	fmt.Println("🎯 Testing System Prompt:")
	fmt.Println("--------------------")
	systemPrompt, err := promptManager.GetSystemPrompt()
	if err != nil {
		log.Printf("Error getting system prompt: %v", err)
	} else {
		// 只显示 prompt 的前 200 个字符，避免输出过长
		preview := systemPrompt
		if len(preview) > 200 {
			preview = preview[:200] + "..."
		}
		fmt.Printf("System Prompt Preview: %s\n", preview)
		fmt.Printf("System Prompt Length: %d characters\n", len(systemPrompt))
	}
	fmt.Println()

	// 测试监督 Prompt
	fmt.Println("🎯 Testing Supervision Prompt:")
	fmt.Println("--------------------")
	supervisionPrompt, err := promptManager.GetSupervisionPrompt()
	if err != nil {
		log.Printf("Error getting supervision prompt: %v", err)
	} else {
		fmt.Printf("Supervision Prompt:\n%s\n", supervisionPrompt)
		fmt.Printf("Supervision Prompt Length: %d characters\n", len(supervisionPrompt))
	}
	fmt.Println()

	fmt.Println("✅ Prompt System Test Completed!")
}

func testIndexBuild() {
	fmt.Println("🧪 Testing Index Build Functionality...")
	fmt.Println()

	// 获取 Git 信息
	gitInfo, err := utils.GetGitInfo()
	if err != nil {
		log.Printf("Warning: Failed to get git info: %v", err)
		gitInfo = &utils.GitInfo{
			RemoteURL: "unknown",
			Branch:    "unknown",
			CommitID:  "unknown",
		}
	}

	fmt.Println("📋 Git Information:")
	fmt.Printf("  Remote URL: %s\n", gitInfo.RemoteURL)
	fmt.Printf("  Branch: %s\n", gitInfo.Branch)
	fmt.Printf("  Commit ID: %s\n", gitInfo.CommitID)
	fmt.Println()

	if gitInfo.RemoteURL == "unknown" || gitInfo.RemoteURL == "" {
		fmt.Println("❌ No valid git remote URL found, cannot test index build")
		return
	}

	if gitInfo.Branch == "unknown" || gitInfo.Branch == "" {
		fmt.Println("❌ No valid git branch found, cannot test index build")
		return
	}

	// 测试索引构建
	fmt.Println("🔍 Testing index build for current repository...")
	fmt.Printf("Repository: %s\n", gitInfo.RemoteURL)
	fmt.Printf("Branch: %s\n", gitInfo.Branch)
	fmt.Println()

	indexExists := utils.EnsureIndexExists(gitInfo.RemoteURL, gitInfo.Branch)
	if indexExists {
		fmt.Println("✅ Index Build Test Completed Successfully!")
	} else {
		fmt.Println("❌ Index Build Test Failed!")
	}
} 