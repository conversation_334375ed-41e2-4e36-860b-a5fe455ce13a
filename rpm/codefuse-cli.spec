Name: codefuse-cli
Version: 0.18.0
Release: %(echo $RELEASE)
# if you want use the parameter of rpm_create on build time,
# uncomment below
Summary: codefuse-cli - AI-powered code assistant command line tool
Group: application/development
License: Commercial
AutoReqProv: none
%define _prefix /usr/local/bin

# uncomment below, if depend on other packages

#Requires: package_name = 1.0.0


%description
# if you want publish current svn URL or Revision use these macros
codefuse-cli - AI-powered code assistant command line tool for enhanced development productivity

# support debuginfo package, to reduce runtime package size

%build
cd $OLDPWD/../
#sudo chown -R admin.admin /go
gobuildpath='/go/src/codefuse-cli/'
mkdir -p $gobuildpath
buildPath='/go/src/codefuse-cli/codefuse-cli'
source=`pwd`
if [ ! -d $buildPath ]; then
    ln -s  $source $buildPath
fi
cd $buildPath
PATH=/usr/local/go/bin:$PATH

# https://docs.redhat.com/zh_hans/documentation/red_hat_enterprise_linux/9/html/packaging_and_distributing_software/rpm-conditionals_advanced-topics
%ifarch aarch64
make BUILD_TARGET=linux BUILD_ARCH=arm64 build
%else
make BUILD_TARGET=linux BUILD_ARCH=amd64 build
%endif

# prepare your files
%install
# OLDPWD is the dir of rpm_create running
# _prefix is an inner var of rpmbuild,
# can set by rpm_create, default is "/home/<USER>"
# _lib is an inner var, maybe "lib" or "lib64" depend on OS

# create dirs
mkdir -p $RPM_BUILD_ROOT%{_prefix}
mkdir -p $RPM_BUILD_ROOT%{_share}
buildPath='/go/src/codefuse-cli/codefuse-cli'
cp $buildPath/bin/codefuse-cli $RPM_BUILD_ROOT%{_prefix}

%files
# set file attribute here
%defattr(-,root,root)
# need not list every file here, keep it as this
%{_prefix}/codefuse-cli
## create an empy dir

# %dir %{_prefix}/var/log

## need bakup old config file, so indicate here

# %config %{_prefix}/etc/sample.conf

## or need keep old config file, so indicate with "noreplace"

# %config(noreplace) %{_prefix}/etc/sample.conf

## indicate the dir for crontab

# %{_crondir}

%post
#define the scripts for post install
%postun
#define the scripts for post uninstall