package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"codefuse-cli/utils"
	"github.com/sashabaranov/go-openai"
)

// runSingleTurnChat 运行单轮对话模式
func runSingleTurnChat(userInput string) {
	// 获取当前工作目录作为工作空间根目录
	workspaceRoot, err := os.Getwd()
	if err != nil {
		fmt.Printf("Failed to get current directory: %v\n", err)
		os.Exit(1)
	}

	// 加载配置
	config, err := utils.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	chatService := NewChatService(config, workspaceRoot)

	// 初始化系统 prompt
	systemPrompt, err := chatService.promptManager.GetSystemPrompt()
	if err != nil {
		log.Printf("Warning: Failed to get system prompt: %v", err)
		systemPrompt = "You are a helpful AI assistant."
	}

	supervisionPrompt, err := chatService.promptManager.GetSupervisionPrompt()
	if err != nil {
		log.Printf("Warning: Failed to get supervision prompt: %v", err)
		supervisionPrompt = ""
	}

	// 初始化消息历史，包含系统 prompt
	var messages []openai.ChatCompletionMessage
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: systemPrompt,
	})

	// 如果有 supervision prompt，添加为用户消息
	if supervisionPrompt != "" {
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: supervisionPrompt,
		})
	}

	fmt.Printf("🤖 处理您的请求: %s\n\n", userInput)

	// 打印可用的工具详细信息
	chatService.printToolsInfo()

	// 检查远程索引配置
	chatService.ensureRemoteIndexIfNeeded()

	fmt.Println("----------------------------------------")

	// 添加用户消息
	userMessage := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: userInput,
	}
	messages = append(messages, userMessage)

	// 流式聊天
	ctx := context.Background()
	_, err = chatService.streamChat(ctx, messages)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n✅ 处理完成")
} 