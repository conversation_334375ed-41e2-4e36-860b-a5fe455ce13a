package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"

	"codefuse-cli/tools"
	"codefuse-cli/tools/augment"
	claude_code "codefuse-cli/tools/claude_code"
)

func main() {
	// 初始化Provider系统
	initProviders()
	
	// 检查帮助选项
	if len(os.Args) > 1 && (os.Args[1] == "--help" || os.Args[1] == "-h") {
		showHelp()
		return
	}

	// 检查是否是HTTP服务器模式
	if len(os.Args) > 1 && os.Args[1] == "--port" {
		if len(os.Args) < 3 {
			fmt.Println("Error: Port number required when using --port")
			fmt.Println("Usage: codefuse-cli --port <port_number> [--wait_project_path]")
			os.Exit(1)
		}
		
		port := os.Args[2]
		waitProjectPath := false
		
		// 检查是否有 --wait_project_path 参数
		for i := 3; i < len(os.Args); i++ {
			if os.Args[i] == "--wait_project_path" {
				waitProjectPath = true
				break
			}
		}
		
		// 如果需要等待项目路径，先处理工作目录切换
		if waitProjectPath {
			err := waitAndChangeToProjectPath()
			if err != nil {
				fmt.Printf("Error changing to project path: %v\n", err)
				os.Exit(1)
			}
		}
		
		runHTTPServer(port)
		return
	}

	// 检查是否是测试模式
	if len(os.Args) > 1 && os.Args[1] == "test" {
		testRemoteRetriever()
		return
	}

	if len(os.Args) > 1 && os.Args[1] == "test-prompt" {
		testPromptSystem()
		return
	}

	if len(os.Args) > 1 && os.Args[1] == "test-index" {
		testIndexBuild()
		return
	}

	// 检查是否有 --input 参数
	if hasInputFileArg() {
		runInteractiveChat()
		return
	}

	// 检查是否是单轮对话模式
	if len(os.Args) > 1 && !strings.HasPrefix(os.Args[1], "test") && os.Args[1] != "--port" {
		// 将所有参数拼接为用户输入
		userInput := strings.Join(os.Args[1:], " ")
		runSingleTurnChat(userInput)
		return
	}

	// 如果没有参数，启动交互模式
	runInteractiveChat()
}

// waitAndChangeToProjectPath 等待并切换到项目路径
func waitAndChangeToProjectPath() error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get home directory: %v", err)
	}
	
	projectPathFile := filepath.Join(homeDir, ".project_path")
	fmt.Printf("🔍 Waiting for project path file: %s\n", projectPathFile)
	
	var projectPath string
	
	// 第一步：循环等待文件创建
	for {
		if _, err := os.Stat(projectPathFile); err == nil {
			// 文件存在，读取内容
			content, err := ioutil.ReadFile(projectPathFile)
			if err != nil {
				return fmt.Errorf("failed to read project path file: %v", err)
			}
			
			projectPath = strings.TrimSpace(string(content))
			if projectPath == "" {
				return fmt.Errorf("project path file is empty")
			}
			
			fmt.Printf("📄 Found project path: %s\n", projectPath)
			break
		}
		
		// 文件不存在，等待一秒后重试
		fmt.Print(".")
		time.Sleep(1 * time.Second)
	}
	
	// 第二步：等待项目路径目录存在
	fmt.Printf("🔍 Waiting for project directory to exist: %s\n", projectPath)
	for {
		if _, err := os.Stat(projectPath); err == nil {
			// 目录存在，切换工作目录
			err = os.Chdir(projectPath)
			if err != nil {
				return fmt.Errorf("failed to change directory to %s: %v", projectPath, err)
			}
			
			fmt.Printf("✅ Changed working directory to: %s\n", projectPath)
			return nil
		}
		
		// 目录不存在，等待一秒后重试
		fmt.Print(".")
		time.Sleep(1 * time.Second)
	}
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("🤖 CodeFuse CLI - AI Code Assistant")
	fmt.Println()
	fmt.Println("USAGE:")
	fmt.Println("  codefuse-cli [OPTIONS] [QUERY]")
	fmt.Println("  codefuse-cli                              # 启动交互模式")
	fmt.Println("  codefuse-cli \"your question\"             # 单轮对话模式")
	fmt.Println("  codefuse-cli --port <port>                # 启动HTTP服务器模式")
	fmt.Println("  codefuse-cli --port <port> --wait_project_path  # 启动HTTP服务器，等待项目路径")
	fmt.Println()
	fmt.Println("OPTIONS:")
	fmt.Println("  -h, --help                     显示此帮助信息")
	fmt.Println("  --port <port>                  启动HTTP服务器模式，监听指定端口")
	fmt.Println("  --wait_project_path            等待~/.project_path文件并切换工作目录")
	fmt.Println()
	fmt.Println("COMMANDS:")
	fmt.Println("  test                           测试远程检索功能")
	fmt.Println("  test-prompt                    测试提示系统和Git集成")
	fmt.Println("  test-index                     测试索引构建功能")
	fmt.Println()
	fmt.Println("ENVIRONMENT VARIABLES:")
	fmt.Println()
	fmt.Println("  AI Model Configuration:")
	fmt.Println("    CODEFUSE_MODEL               AI模型名称 (默认: deepseek-chat)")
	fmt.Println("    CODEFUSE_API_KEY             API密钥")
	fmt.Println("    CODEFUSE_BASE_URL            API基础URL (默认: https://api.deepseek.com/v1)")
	fmt.Println()
	fmt.Println("  OpenAI Compatibility:")
	fmt.Println("    OPENAI_API_KEY               OpenAI API密钥 (如果未设置CODEFUSE_API_KEY)")
	fmt.Println("    OPENAI_BASE_URL              OpenAI API基础URL (如果未设置CODEFUSE_BASE_URL)")
	fmt.Println()
	fmt.Println("  Provider Configuration:")
	fmt.Println("    CODEFUSE_PROVIDER            算法提供者选择 (默认: augment)")
	fmt.Println("                                 可选值: augment, claude_code")
	fmt.Println()
	fmt.Println("  Other Configuration:")
	fmt.Println("    SEARCH_RESPONSE_TRUNCATION_SIZE  搜索响应截断大小 (默认: 20000)")
	fmt.Println()
	fmt.Println("EXAMPLES:")
	fmt.Println()
	fmt.Println("  # 使用默认配置启动交互模式")
	fmt.Println("  codefuse-cli")
	fmt.Println()
	fmt.Println("  # 单轮对话")
	fmt.Println("  codefuse-cli \"帮我分析这个函数的作用\"")
	fmt.Println()
	fmt.Println("  # 启动HTTP服务器模式")
	fmt.Println("  codefuse-cli --port 8080")
	fmt.Println()
	fmt.Println("  # 启动HTTP服务器并等待项目路径")
	fmt.Println("  codefuse-cli --port 8080 --wait_project_path")
	fmt.Println()
	fmt.Println("  # 使用自定义模型")
	fmt.Println("  CODEFUSE_MODEL=gpt-4o-mini codefuse-cli \"重构这段代码\"")
	fmt.Println()
	fmt.Println("  # 使用OpenAI配置")
	fmt.Println("  OPENAI_API_KEY=your-key OPENAI_BASE_URL=https://api.openai.com/v1 \\")
	fmt.Println("    codefuse-cli \"解释这个算法\"")
	fmt.Println()
	fmt.Println("  # 使用自定义API")
	fmt.Println("  CODEFUSE_API_KEY=your-key CODEFUSE_BASE_URL=https://your-api.com/v1 \\")
	fmt.Println("    codefuse-cli \"生成单元测试\"")
	fmt.Println()
	fmt.Println("  # 使用Claude Code Provider")
	fmt.Println("  CODEFUSE_PROVIDER=claude_code codefuse-cli \"分析代码\"")
	fmt.Println()
	fmt.Println("  # 启动HTTP服务器使用指定Provider")
	fmt.Println("  CODEFUSE_PROVIDER=claude_code codefuse-cli --port 8080")
	fmt.Println()
	fmt.Println("FEATURES:")
	fmt.Println("  🤖 AI代码分析和生成")
	fmt.Println("  🔍 远程代码库检索 (支持 code.alipay.com)")
	fmt.Println("  🔗 符号定义和调用查找")
	fmt.Println("  📝 文件编辑和管理")
	fmt.Println("  🛠️ 丰富的工具集成")
	fmt.Println("  📁 动态工作目录切换 (--wait_project_path)")
	fmt.Println()
	fmt.Println("PROJECT PATH MODE:")
	fmt.Println("  当使用 --wait_project_path 参数时，程序会：")
	fmt.Println("  1. 等待 ~/.project_path 文件创建")
	fmt.Println("  2. 读取文件内容作为项目路径")
	fmt.Println("  3. 切换工作目录到该路径")
	fmt.Println("  4. 然后启动HTTP服务器")
	fmt.Println()
	fmt.Println("CONFIGURATION PRIORITY:")
	fmt.Println("  Environment Variables > Default Values")
	fmt.Println()
	fmt.Println("For more information, visit: https://github.com/your-org/codefuse-cli")
}

// 全局ProviderManager
var globalProviderManager *tools.ProviderManager

// initProviders 初始化所有Providers
func initProviders() {
	globalProviderManager = tools.NewProviderManager()
	
	// 注册所有可用的providers
	globalProviderManager.RegisterProvider(&augment.AugmentProvider{})
	globalProviderManager.RegisterProvider(&claude_code.ClaudeCodeProvider{})
}

// GetGlobalProviderManager 获取全局ProviderManager (供其他文件使用)
func GetGlobalProviderManager() *tools.ProviderManager {
	if globalProviderManager == nil {
		initProviders()
	}
	return globalProviderManager
}

// hasInputFileArg 检查是否有 --input 参数
func hasInputFileArg() bool {
	for _, arg := range os.Args[1:] {
		if strings.HasPrefix(arg, "--input=") {
			return true
		}
	}
	return false
}
