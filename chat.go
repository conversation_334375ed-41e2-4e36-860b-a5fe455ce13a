package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"strings"

	"codefuse-cli/tools"
	"codefuse-cli/utils"
	"github.com/sashabaranov/go-openai"
)

type ChatService struct {
	client        *openai.Client
	toolManager   tools.ToolManager
	promptManager tools.PromptManager
	gitInfo       *utils.GitInfo
	config        *utils.Config
	providerName  string
	workspaceRoot string
}

func NewChatService(config *utils.Config, workspaceRoot string) *ChatService {
	clientConfig := openai.DefaultConfig(config.AI.APIKey)
	if config.AI.BaseURL != "" {
		clientConfig.BaseURL = config.AI.BaseURL
	}

	client := openai.NewClientWithConfig(clientConfig)

	// 从中心化的Git服务获取Git信息
	gitService := utils.GetGlobalGitService()
	gitInfo := gitService.GetGitInfo()

	// 获取全局ProviderManager
	providerManager := GetGlobalProviderManager()
	
	// 从环境变量获取要使用的provider
	providerName := providerManager.GetProviderFromEnv()
	
	// 打印可用的providers
	providerManager.PrintAvailableProviders()

	// 创建Provider配置
	providerConfig := &tools.ProviderConfig{
		WorkspaceRoot: workspaceRoot,
		Config:        config,
		GitInfo:       gitInfo,
	}

	// 创建ToolManager和PromptManager
	toolManager, err := providerManager.CreateToolManager(providerName, providerConfig)
	if err != nil {
		fmt.Printf("Error creating tool manager: %v\n", err)
		fmt.Printf("Falling back to default provider: %s\n", providerManager.GetDefaultProvider())
		providerName = providerManager.GetDefaultProvider()
		toolManager, _ = providerManager.CreateToolManager(providerName, providerConfig)
	}

	promptManager, err := providerManager.CreatePromptManager(providerName, providerConfig)
	if err != nil {
		fmt.Printf("Error creating prompt manager: %v\n", err)
		fmt.Printf("Falling back to default provider: %s\n", providerManager.GetDefaultProvider())
		providerName = providerManager.GetDefaultProvider()
		promptManager, _ = providerManager.CreatePromptManager(providerName, providerConfig)
	}

	return &ChatService{
		client:        client,
		toolManager:   toolManager,
		promptManager: promptManager,
		gitInfo:       gitInfo,
		config:        config,
		providerName:  providerName,
		workspaceRoot: workspaceRoot,
	}
}

func (cs *ChatService) executeFunction(name string, arguments string) (string, error) {
	return cs.toolManager.ExecuteTool(name, arguments)
}

// RefreshGitInfo 刷新git信息并更新相关组件
func (cs *ChatService) RefreshGitInfo() error {
	// 从中心化的Git服务刷新Git信息
	gitService := utils.GetGlobalGitService()
	gitInfo := gitService.RefreshGitInfo()

	// 更新本地gitInfo引用
	cs.gitInfo = gitInfo

	// 方法1：直接刷新现有PromptManager的Git信息
	if cs.promptManager != nil {
		err := cs.promptManager.RefreshGitInfo()
		if err != nil {
			log.Printf("Warning: Failed to refresh prompt manager git info: %v", err)
		}
	}

	// 方法2：如果需要完全重新创建组件（例如工具配置可能依赖于Git信息）
	// 获取全局ProviderManager
	providerManager := GetGlobalProviderManager()

	// 创建新的Provider配置
	providerConfig := &tools.ProviderConfig{
		WorkspaceRoot: cs.workspaceRoot,
		Config:        cs.config,
		GitInfo:       gitInfo,
	}

	// 重新创建ToolManager
	toolManager, err := providerManager.CreateToolManager(cs.providerName, providerConfig)
	if err != nil {
		log.Printf("Error recreating tool manager: %v", err)
		return err
	}

	// 更新ToolManager
	cs.toolManager = toolManager

	return nil
}

func (cs *ChatService) streamChat(ctx context.Context, messages []openai.ChatCompletionMessage) ([]openai.ChatCompletionMessage, error) {
	req := openai.ChatCompletionRequest{
		Model:    cs.config.AI.Model,
		Messages: messages,
		Tools:    cs.toolManager.GetTools(),
		Stream:   true,
	}

	stream, err := cs.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		return messages, fmt.Errorf("failed to create stream: %v", err)
	}
	defer stream.Close()

	var functionCalls []openai.ToolCall
	var assistantMessage strings.Builder

	fmt.Print("Codefuse正在处理你的请求\n")

	for {
		response, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				break
			}
			return messages, fmt.Errorf("stream error: %v", err)
		}

		if len(response.Choices) == 0 {
			continue
		}

		choice := response.Choices[0]
		delta := choice.Delta

		// 处理普通文本内容
		if delta.Content != "" {
			fmt.Print(delta.Content)
			assistantMessage.WriteString(delta.Content)
		}

		// 处理工具调用
		if len(delta.ToolCalls) > 0 {
			for _, toolCall := range delta.ToolCalls {
				// 确保 Index 不为空
				if toolCall.Index == nil {
					continue
				}

				// 查找或创建对应的工具调用
				for len(functionCalls) <= *toolCall.Index {
					functionCalls = append(functionCalls, openai.ToolCall{})
				}

				existingCall := &functionCalls[*toolCall.Index]

				// 设置基本信息
				if toolCall.ID != "" {
					existingCall.ID = toolCall.ID
					existingCall.Type = toolCall.Type
				}

				// 处理 Function 字段
				if toolCall.Function.Name != "" || toolCall.Function.Arguments != "" {
					if existingCall.Function.Name == "" {
						existingCall.Function.Name = toolCall.Function.Name
					}

					if toolCall.Function.Arguments != "" {
						existingCall.Function.Arguments += toolCall.Function.Arguments
					}
				}
			}
		}
	}

	fmt.Println() // 换行

	// 如果有工具调用，执行它们
	if len(functionCalls) > 0 {
		// 添加助手消息（包含工具调用）
		assistantMsg := openai.ChatCompletionMessage{
			Role:      openai.ChatMessageRoleAssistant,
			Content:   assistantMessage.String(),
			ToolCalls: functionCalls,
		}
		messages = append(messages, assistantMsg)

		// 执行工具调用并添加结果
		for _, toolCall := range functionCalls {
			if toolCall.Function.Name != "" {
				fmt.Printf("\n🔧 Calling function: %s with arguments: %s\n",
					toolCall.Function.Name, toolCall.Function.Arguments)

				result, err := cs.executeFunction(toolCall.Function.Name, toolCall.Function.Arguments)
				if err != nil {
					result = fmt.Sprintf("Error: %v", err)
				}

				fmt.Printf("📊 Function result: %s\n\n", result)

				// 添加工具调用结果消息
				toolMsg := openai.ChatCompletionMessage{
					Role:       openai.ChatMessageRoleTool,
					Content:    result,
					ToolCallID: toolCall.ID,
				}
				messages = append(messages, toolMsg)
			}
		}

		// 再次调用模型获取最终回复
		fmt.Print("Codefuse: \n")
		return cs.streamChat(ctx, messages)
	} else {
		// 没有工具调用时，仍需要保存助手的回复到消息历史中
		if assistantMessage.Len() > 0 {
			assistantMsg := openai.ChatCompletionMessage{
				Role:    openai.ChatMessageRoleAssistant,
				Content: assistantMessage.String(),
			}
			messages = append(messages, assistantMsg)
		}
	}

	return messages, nil
}

// printToolsInfo 打印可用工具信息
func (cs *ChatService) printToolsInfo() {
	fmt.Printf("🎯 Current Provider: %s\n", cs.providerName)
	fmt.Println("🛠️  Available Tools:")
	toolNames := cs.toolManager.GetToolNames()
	fmt.Printf("📊 Total tools registered: %d\n", len(toolNames))

	// 按类别显示工具
	basicTools := []string{}
	fileTools := []string{}
	remoteTools := []string{}
	claudeTools := []string{}

	for _, toolName := range toolNames {
		switch toolName {
		case "add", "bash":
			basicTools = append(basicTools, toolName)
		case "save_file", "view_file", "str_replace_editor_flattened":
			fileTools = append(fileTools, toolName)
		case "remote_retriever", "symbol_retrieval":
			remoteTools = append(remoteTools, toolName)
		default:
			claudeTools = append(claudeTools, toolName)
		}
	}

	if len(basicTools) > 0 {
		fmt.Printf("  📝 Basic Tools: %v\n", basicTools)
	}
	if len(fileTools) > 0 {
		fmt.Printf("  📁 File Operation Tools: %v\n", fileTools)
	}
	if len(remoteTools) > 0 {
		fmt.Printf("  🌐 Remote Retrieval Tools: %v\n", remoteTools)
	}
	if len(claudeTools) > 0 {
		fmt.Printf("  🔧 Claude Tools: %v\n", claudeTools)
	}
	if len(remoteTools) == 0 && cs.providerName == "augment" {
		fmt.Println("  🚫 Remote Retrieval Tools: Disabled")
	}
}

// printDetailedToolsInfo 打印详细的工具信息
func (cs *ChatService) printDetailedToolsInfo() {
	fmt.Println("🛠️  Available Tools:")
	toolNames := cs.toolManager.GetToolNames()
	fmt.Printf("📊 Total tools registered: %d\n", len(toolNames))

	// 按类别显示工具
	basicTools := []string{}
	fileTools := []string{}
	remoteTools := []string{}

	for _, toolName := range toolNames {
		switch toolName {
		case "add", "bash":
			basicTools = append(basicTools, toolName)
		case "save_file", "view_file", "str_replace_editor_flattened":
			fileTools = append(fileTools, toolName)
		case "remote_retriever", "symbol_retrieval":
			remoteTools = append(remoteTools, toolName)
		}
	}

	// 显示基础工具
	fmt.Println("  📝 Basic Tools:")
	for _, tool := range basicTools {
		switch tool {
		case "add":
			fmt.Println("    - add(a, b): adds two numbers")
		case "bash":
			fmt.Println("    - bash(command): execute bash commands in terminal")
		}
	}

	// 显示文件操作工具
	fmt.Println("  📁 File Operation Tools:")
	for _, tool := range fileTools {
		switch tool {
		case "save_file":
			fmt.Println("    - save_file(file_path, file_content, add_last_line_newline): save a new file")
		case "view_file":
			fmt.Println("    - view_file(file_path): view complete file content")
		case "str_replace_editor_flattened":
			fmt.Println("    - str_replace_editor_flattened(path, str_replace_entries): advanced string replacement editor for editing files")
		}
	}

	// 显示远程检索工具
	if len(remoteTools) > 0 {
		fmt.Println("  🌐 Remote Retrieval Tools:")
		for _, tool := range remoteTools {
			switch tool {
			case "remote_retriever":
				fmt.Println("    - remote_retriever(query, repo_url, branch): search remote codebase for relevant code snippets")
			case "symbol_retrieval":
				fmt.Println("    - symbol_retrieval(repo_url, branch, retrival_type, method_name, class_name): search for symbol definitions or usages")
			}
		}
	} else {
		fmt.Println("  🚫 Remote Retrieval Tools: Disabled (not a supported git repository)")
	}
}

// ensureRemoteIndexIfNeeded 统一处理远程索引检查逻辑
func (cs *ChatService) ensureRemoteIndexIfNeeded() {
	hasRemoteRetriever := cs.toolManager.HasTool("remote_retriever")
	shouldEnsureIndex := hasRemoteRetriever &&
		cs.gitInfo.RemoteURL != "unknown" && cs.gitInfo.RemoteURL != "" &&
		cs.gitInfo.Branch != "unknown" && cs.gitInfo.Branch != ""

	fmt.Printf("🔍 Remote Index Configuration:\n")
	fmt.Printf("  Remote Retriever Tool Available: %t\n", hasRemoteRetriever)
	fmt.Printf("  Git Remote URL: %s\n", cs.gitInfo.RemoteURL)
	fmt.Printf("  Git Branch: %s\n", cs.gitInfo.Branch)
	fmt.Printf("  Should Ensure Remote Index: %t\n", shouldEnsureIndex)

	if shouldEnsureIndex {
		fmt.Println()
		fmt.Println("🔄 Checking remote codebase index...")

		indexExists := utils.EnsureIndexExists(cs.gitInfo.RemoteURL, cs.gitInfo.Branch)
		if indexExists {
			fmt.Println("✅ Remote codebase index is ready")
		} else {
			fmt.Println("⚠️  Remote codebase index build failed or timed out")
			fmt.Println("   Remote retrieval might not work properly")
		}
	} else {
		fmt.Println("⏭️  Skipping remote index check (conditions not met)")
	}
} 