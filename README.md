# CodeFuse CLI

🤖 AI 代码助手工具 - 集成代码分析、生成和远程检索功能

## 概述

CodeFuse CLI 是一个基于 Go 的 AI 代码助手工具，集成了 DeepSeek API 和多种实用工具，包括远程代码库检索、符号查找、文件编辑等功能。

## 功能特性

- 🤖 **AI 代码分析和生成** - 支持多种 AI 模型
- 🔍 **远程代码库检索** - 语义搜索，支持 code.alipay.com
- 🔗 **符号定义和调用查找** - 精确定位函数定义和使用位置
- 📝 **文件编辑和管理** - 智能文件操作工具
- 🛠️ **丰富的工具集成** - 多种内置工具支持
- 💻 **多模式交互** - 支持交互式和单轮对话模式

## 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd codefuse-cli

# 构建
go build -o codefuse-cli

# 移动到可执行目录，你就可以在任意电脑的终端使用codefuse-cli
sudo mv codefuse-cli /usr/local/bin/     
# 获取帮助
./codefuse-cli --help
```

### 基本使用

```bash
# 交互模式
./codefuse-cli

# 单轮对话
./codefuse-cli "帮我分析这个函数的作用"

# 显示帮助
./codefuse-cli --help
```

## 配置说明

### 环境变量配置

支持通过环境变量配置 AI 模型相关参数：

#### CodeFuse 专用环境变量
```bash
export CODEFUSE_MODEL="deepseek-chat"              # AI模型名称
export CODEFUSE_API_KEY="your-api-key"             # API密钥
export CODEFUSE_BASE_URL="https://api.deepseek.com/v1"  # API基础URL
```

#### 兼容 OpenAI 环境变量
```bash
export OPENAI_API_KEY="your-openai-api-key"        # OpenAI API密钥
export OPENAI_BASE_URL="https://api.openai.com/v1" # OpenAI API基础URL
```

#### 其他配置
```bash
export SEARCH_RESPONSE_TRUNCATION_SIZE="20000"     # 搜索响应截断大小
```

### 配置优先级

**环境变量 > 默认值**

- 优先使用 `CODEFUSE_*` 环境变量
- 如果未设置，则使用对应的 `OPENAI_*` 环境变量
- 如果都未设置，使用默认的 DeepSeek 配置



## 使用方法

### 命令选项

```bash
./codefuse-cli [OPTIONS] [QUERY]
```

#### 选项
- `-h, --help` - 显示帮助信息

#### 命令
- `test` - 测试远程检索功能
- `test-prompt` - 测试提示系统和Git集成
- `test-index` - 测试索引构建功能

### 使用示例

```bash
# 使用默认配置启动交互模式
./codefuse-cli

# 单轮对话
./codefuse-cli "帮我分析这个函数的作用"

# 使用自定义模型
CODEFUSE_MODEL=gpt-4o-mini ./codefuse-cli "重构这段代码"

# 使用 OpenAI 配置
OPENAI_API_KEY=your-key OPENAI_BASE_URL=https://api.openai.com/v1 \
  ./codefuse-cli "解释这个算法"

# 使用自定义 API
CODEFUSE_API_KEY=your-key CODEFUSE_BASE_URL=https://your-api.com/v1 \
  ./codefuse-cli "生成单元测试"
```

## 内置工具

1. **add(a, b)** - 数学计算工具
2. **save_file(file_path, file_content, add_last_line_newline)** - 文件保存工具
3. **view_file(file_path)** - 文件查看工具
4. **str_replace_editor_flattened(path, str_replace_entries)** - 高级字符串替换编辑器
5. **remote_retriever(query, repo_url, branch)** - 远程代码库检索工具（需要 code.alipay.com 仓库）
6. **symbol_retrieval(repo_url, branch, retrival_type, method_name, class_name)** - 符号检索工具

## 远程代码检索

### 功能特点

- **语义搜索** - 使用高级嵌入模型查找相关代码片段
- **多语言支持** - 支持 Python、Java、JavaScript、TypeScript、Go、Rust 等
- **智能过滤** - 基于 LLM 的结果过滤
- **智能排序** - 按相关性对结果进行排序
- **并发处理** - 并行处理多个代码块以提高性能

### 使用要求

- 仓库必须托管在 code.alipay.com
- 当前目录必须是 git 仓库
- 需要有效的远程检索 API 访问权限

## 开发和测试

### 构建项目

```bash
go mod tidy
go build -o codefuse-cli
```

### 运行测试

```bash
# 测试远程检索功能
./codefuse-cli test

# 测试提示系统
./codefuse-cli test-prompt

# 测试索引构建
./codefuse-cli test-index
```

## 项目结构

```
.
├── main.go                           # 主应用程序
├── test.go                           # 测试功能
├── utils/
│   ├── config.go                     # 配置管理
│   └── git.go                        # Git 信息获取
├── tools/
│   ├── tools.go                      # 工具管理器
│   ├── remote_retriver.go            # 远程代码检索
│   ├── codebase_retrival_symbol.go   # 符号检索
│   ├── add.go                        # 数学工具
│   ├── save_file.go                  # 文件保存工具
│   ├── view_file.go                  # 文件查看工具
│   └── str_replace_editor.go         # 字符串替换编辑器
├── pkg/                              # 扩展包
├── go.mod                            # Go 模块定义
├── go.sum                            # 依赖校验
└── README.md                         # 项目文档
```

## 依赖项

- `github.com/sashabaranov/go-openai` - OpenAI API 客户端，用于 DeepSeek 集成
- Go 标准库 - HTTP、JSON 和并发处理

## 错误处理

工具包含完善的错误处理机制：

- API 认证失败
- 网络超时
- 无效响应格式
- 缺失配置文件
- LLM 处理错误

发生错误时，工具提供回退行为和详细的错误信息。

## 许可证

请参阅 [LEGAL.md](LEGAL.md) 文件了解许可证信息。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。 