# CodeFuse CLI HTTP服务器使用指南

## 🚀 启动HTTP服务器

```bash
# 启动HTTP服务器，监听8080端口
./codefuse-cli --port 8080

# 或者使用其他端口
./codefuse-cli --port 3000
```

服务器将监听 `0.0.0.0:端口`，支持公网访问。

## 📡 API接口

### 1. 聊天补全接口
- **URL**: `POST /v1/chat/completions`  
- **功能**: 与AI模型对话，支持工具调用
- **格式**: 支持自定义格式和OpenAI兼容格式

### 2. 健康检查接口
- **URL**: `GET /health`
- **功能**: 检查服务器状态

## 📝 请求格式

### 自定义格式
```json
{
  "query": "帮我分析main.go文件的结构",
  "taskid": "task-123"
}
```

### OpenAI兼容格式
```json
{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user",
      "content": "帮我分析main.go文件的结构"
    }
  ],
  "stream": true
}
```

## 🔄 响应格式

服务器使用Server-Sent Events (SSE) 格式返回流式响应：

```
data: {"id":"chatcmpl-task-123-**********","object":"chat.completion.chunk","created":**********,"model":"deepseek-chat","choices":[{"index":0,"delta":{"role":"assistant","content":"我来帮您分析"}}]}

data: {"id":"chatcmpl-task-123-**********","object":"chat.completion.chunk","created":**********,"model":"deepseek-chat","choices":[{"index":0,"delta":{"content":"main.go"}}]}

data: [DONE]
```

## 🛠️ 工具执行

当AI需要调用工具时，工具的执行信息也会通过SSE流式返回：

```
data: {"choices":[{"delta":{"content":"🔧 Calling function: view_file with arguments: {\"file_path\":\"main.go\"}"}}]}

data: {"choices":[{"delta":{"content":"📊 Function result: [文件内容]"}}]}
```

## 🧪 测试示例

### 使用Python测试
```bash
# 使用提供的测试脚本
python test_http_api.py http://localhost:8080 "分析main.go文件"
```

### 使用curl测试
```bash
# 自定义格式
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"query":"Hello","taskid":"test-123"}' \
  --no-buffer

# OpenAI格式
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}],"stream":true}' \
  --no-buffer

# 健康检查
curl http://localhost:8080/health
```

### 使用JavaScript/Node.js测试
```javascript
const response = await fetch('http://localhost:8080/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    query: "帮我分析代码结构",
    taskid: "web-client-123"
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { value, done } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') {
        console.log('Stream completed');
        break;
      }
      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices?.[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }
  }
}
```

## 🔧 环境变量配置

```bash
# AI模型配置
export CODEFUSE_MODEL="deepseek-chat"
export CODEFUSE_API_KEY="your-api-key"
export CODEFUSE_BASE_URL="https://api.deepseek.com/v1"

# 或使用OpenAI配置
export OPENAI_API_KEY="your-openai-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"

# 启动服务器
./codefuse-cli --port 8080
```

## 🌟 功能特性

- ✅ **多格式支持**: 自定义格式 + OpenAI兼容格式
- ✅ **流式响应**: 实时返回AI生成内容
- ✅ **工具集成**: 支持文件操作、代码检索等工具
- ✅ **工具执行流**: 工具调用过程也会流式返回
- ✅ **CORS支持**: 支持跨域请求
- ✅ **公网访问**: 监听0.0.0.0，可通过公网访问
- ✅ **错误处理**: 完善的错误处理机制
- ✅ **健康检查**: 服务状态监控

## 🔍 可用工具

HTTP模式下支持所有命令行模式的工具：

- **文件操作**: `view_file`, `save_file`, `str_replace_editor_flattened`
- **命令执行**: `bash`
- **代码检索**: `remote_retriever`, `symbol_retrieval` (如果配置了远程仓库)
- **基础工具**: `add` (示例工具)

## 📊 监控和日志

服务器会输出详细的日志信息：
- 请求处理状态
- 工具调用详情
- 错误信息
- 性能统计

## ⚡ 性能优化

- 流式响应减少延迟
- 并发请求支持
- 内存优化的工具执行
- 高效的消息处理机制 