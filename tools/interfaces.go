package tools

import (
	"github.com/sashabaranov/go-openai"
	"codefuse-cli/utils"
)

// Tool 定义工具接口
type Tool interface {
	GetDefinition() openai.Tool
	Execute(arguments string) (string, error)
}

// ToolManager 工具管理器接口
type ToolManager interface {
	RegisterTool(name string, tool Tool)
	GetTools() []openai.Tool
	GetToolNames() []string
	HasTool(name string) bool
	ExecuteTool(name string, arguments string) (string, error)
}

// PromptManager Prompt管理器接口
type PromptManager interface {
	GetSystemPrompt() (string, error)
	GetSupervisionPrompt() (string, error)
	RefreshGitInfo() error // 新增：刷新Git信息的方法
}

// ProviderConfig Provider配置
type ProviderConfig struct {
	WorkspaceRoot string
	Config        *utils.Config
	GitInfo       *utils.GitInfo
}

// Provider 定义算法提供者接口
type Provider interface {
	GetName() string
	GetDescription() string
	CreateToolManager(config *ProviderConfig) ToolManager
	CreatePromptManager(config *ProviderConfig) PromptManager
} 