package augment

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"text/template"
	"time"

	"codefuse-cli/utils"
	"github.com/sasha<PERSON>nov/go-openai"
)

// Prompt templates
const (
	MQueryFilterPromptTemplate = `You are a code retrieval assistant. I will provide you with a user query and some retrieved code documents. Please filter and keep only the code documents that are relevant to the query.
Query: {{.Query}}

Candidates:
{{range $index, $item := .CodebaseRecallResult}}Code Item Index: {{$index}}
Code Item Path: {{index $item 1}}
Code Item Content: {{index $item 2}}
{{end}}

Based on the user's query and the retrieved code documents, identify which documents are relevant to the query. Return only the indexes of the relevant documents, separated by the | character. Do not provide any additional explanation.

If none of the code documents are relevant to the query, return DISCARD_ALL.`

	MQueryRankPromptTemplate = `You are a code retrieval assistant. I will provide you with a user query and some retrieved code documents. Please rank the code documents based on the user's query.
Query: {{.Query}}

Candidates:
{{range $index, $item := .CodebaseRecallResult}}Code Item Index: {{$index}}
Code Item Path: {{index $item 1}}
Code Item Content: {{index $item 2}}
{{end}}

Based on the user's query and the retrieved code documents, reank the code documents based on the relevance to the query in descending order, with the most relevant code document at the top. Return the indexes of the code documents, separated by the | character. Do not provide any additional explanation.
DO NOT DISCARD ANY CODE ITEM.`

	SearchResponsePromptTemplate = `The following code sections were retrieved:

{{range .CodebaseRecallResult}}Path: {{index . 0}}
{{index . 1}}

{{end}}`
)

// CodeChunk represents a code chunk with metadata
type CodeChunk struct {
	RelativePath string `json:"relativePath"`
	Snippet      string `json:"snippet"`
	StartLine    int    `json:"startLine"`
	EndLine      int    `json:"endLine"`
}

// RecallResponse represents the response from the recall API
type RecallResponse struct {
	Success   bool        `json:"success"`
	ErrorCode interface{} `json:"errorCode"` // Can be string or number
	ErrorMsg  string      `json:"errorMsg"`
	Data      struct {
		CodeChunkWithCompleteSchema []CodeChunk `json:"codeChunkWithCompleteSchema"`
	} `json:"data"`
	ErrorType string `json:"errorType"`
}



// RecallRequest represents the request to the recall API
type RecallRequest struct {
	RepoURL string `json:"repoURL"`
	Branch  string `json:"branch"`
	Query   string `json:"query"`
}

// LLMClient interface for making LLM requests
type LLMClient interface {
	Chat(ctx context.Context, messages []openai.ChatCompletionMessage) (string, error)
}

// OpenAILLMClient implements LLMClient using OpenAI client
type OpenAILLMClient struct {
	client *openai.Client
	model  string
}

// NewOpenAILLMClient creates a new OpenAI LLM client
func NewOpenAILLMClient(config *utils.Config) *OpenAILLMClient {
	clientConfig := openai.DefaultConfig(config.AI.APIKey)
	if config.AI.BaseURL != "" {
		clientConfig.BaseURL = config.AI.BaseURL
	}

	return &OpenAILLMClient{
		client: openai.NewClientWithConfig(clientConfig),
		model:  config.AI.Model,
	}
}

// Chat implements the LLMClient interface
func (c *OpenAILLMClient) Chat(ctx context.Context, messages []openai.ChatCompletionMessage) (string, error) {
	req := openai.ChatCompletionRequest{
		Model:    c.model,
		Messages: messages,
		Stream:   false,
	}

	resp, err := c.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to create chat completion: %v", err)
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("no response choices returned")
	}

	content := resp.Choices[0].Message.Content
	// Remove <think> tags if present
	content = strings.ReplaceAll(content, "<think>\n\n</think>\n\n", "")

	return content, nil
}

// CodebaseRetriever handles codebase retrieval operations
type CodebaseRetriever struct {
	codeChunkTemplate string
	extToLang         map[string]string
	config            *utils.Config
	llmClient         LLMClient
	filterTemplate    *template.Template
	rankTemplate      *template.Template
	responseTemplate  *template.Template
}

// NewCodebaseRetriever creates a new CodebaseRetriever instance
func NewCodebaseRetriever() (*CodebaseRetriever, error) {
	return NewCodebaseRetrieverWithConfig(nil)
}

// NewCodebaseRetrieverWithConfig creates a new CodebaseRetriever instance with config
func NewCodebaseRetrieverWithConfig(config *utils.Config) (*CodebaseRetriever, error) {
	codeChunkTemplate := "```{{.Language}}:{{.Path}}[{{.StartLine}}:{{.EndLine}}]\n{{.CodeChunk}}\n```\n"

	extToLang := map[string]string{
		".py":    "Python",
		".java":  "Java",
		".js":    "JavaScript",
		".ts":    "TypeScript",
		".cpp":   "C++",
		".c":     "C",
		".go":    "Go",
		".rs":    "Rust",
		".html":  "HTML",
		".css":   "CSS",
		".json":  "JSON",
		".md":    "Markdown",
		".sh":    "Shell",
		".xml":   "XML",
		".swift": "Swift",
		".kt":    "Kotlin",
		".kts":   "Kotlin",
	}

	// 如果没有提供配置，加载默认配置
	if config == nil {
		var err error
		config, err = utils.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to load config: %v", err)
		}
	}

	// Create LLM client
	llmClient := NewOpenAILLMClient(config)

	// Parse templates
	filterTemplate, err := template.New("filter").Parse(MQueryFilterPromptTemplate)
	if err != nil {
		return nil, fmt.Errorf("failed to parse filter template: %v", err)
	}

	rankTemplate, err := template.New("rank").Parse(MQueryRankPromptTemplate)
	if err != nil {
		return nil, fmt.Errorf("failed to parse rank template: %v", err)
	}

	responseTemplate, err := template.New("response").Parse(SearchResponsePromptTemplate)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response template: %v", err)
	}

	return &CodebaseRetriever{
		codeChunkTemplate: codeChunkTemplate,
		extToLang:         extToLang,
		config:            config,
		llmClient:         llmClient,
		filterTemplate:    filterTemplate,
		rankTemplate:      rankTemplate,
		responseTemplate:  responseTemplate,
	}, nil
}

// FilePathToLanguage converts file path to programming language
func (cr *CodebaseRetriever) FilePathToLanguage(filePath string) string {
	ext := filepath.Ext(filePath)
	if lang, exists := cr.extToLang[ext]; exists {
		return lang
	}
	return "Unknown"
}

// CalculateTokens estimates the token count for a code chunk
func (cr *CodebaseRetriever) CalculateTokens(chunk CodeChunk) int {
	content := fmt.Sprintf("Code Item Index: 0\nCode Item Path: %s\nCode Item Content: %s",
		chunk.RelativePath, chunk.Snippet)
	// Simple token estimation: roughly 4 characters per token
	return len(content) / 4
}

// GroupByTokens groups code chunks by token count
func (cr *CodebaseRetriever) GroupByTokens(codeChunks []CodeChunk, maxContextSize int) [][]CodeChunk {
	var groups [][]CodeChunk
	var currentGroup []CodeChunk
	currentTokens := 0

	for _, chunk := range codeChunks {
		tokenCount := cr.CalculateTokens(chunk)

		// If adding current chunk would exceed context size, save current group and create new one
		if currentTokens+tokenCount > maxContextSize && len(currentGroup) > 0 {
			groups = append(groups, currentGroup)
			currentGroup = []CodeChunk{}
			currentTokens = 0
		}

		// If single chunk exceeds max context, add as separate group
		if tokenCount > maxContextSize {
			groups = append(groups, []CodeChunk{chunk})
			continue
		}

		// Add to current group
		currentGroup = append(currentGroup, chunk)
		currentTokens += tokenCount
	}

	// Add last group if it exists
	if len(currentGroup) > 0 {
		groups = append(groups, currentGroup)
	}

	return groups
}

// GroupByFixedSize groups code chunks by fixed size
func (cr *CodebaseRetriever) GroupByFixedSize(codeChunks []CodeChunk, groupSize int) [][]CodeChunk {
	var groups [][]CodeChunk
	for i := 0; i < len(codeChunks); i += groupSize {
		end := i + groupSize
		if end > len(codeChunks) {
			end = len(codeChunks)
		}
		groups = append(groups, codeChunks[i:end])
	}
	return groups
}

// AsyncMQuery performs async mquery request using LLM
func (cr *CodebaseRetriever) AsyncMQuery(ctx context.Context, query string, groupIdx int, group []CodeChunk, promptTemplate *template.Template) ([]CodeChunk, error) {
	log.Printf("Processing Group %d with %d chunks", groupIdx, len(group))

	// Prepare codebase recall result for template
	var codebaseRecallResult [][]interface{}
	for idx, chunk := range group {
		log.Printf("Group: %d, file_path: %s", groupIdx, chunk.RelativePath)
		codebaseRecallItem := []interface{}{
			idx,
			chunk.RelativePath,
			chunk.Snippet,
		}
		codebaseRecallResult = append(codebaseRecallResult, codebaseRecallItem)
	}

	// Render prompt template
	var promptBuf bytes.Buffer
	templateData := map[string]interface{}{
		"Query":                query,
		"CodebaseRecallResult": codebaseRecallResult,
	}

	if err := promptTemplate.Execute(&promptBuf, templateData); err != nil {
		log.Printf("Filter Group: %d, template error: %v, return whole group", groupIdx, err)
		return group, nil
	}

	prompt := promptBuf.String() + "/no_think"

	// Make LLM request
	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleUser,
			Content: prompt,
		},
	}

	response, err := cr.llmClient.Chat(ctx, messages)
	if err != nil {
		log.Printf("Filter Group: %d, LLM error: %v, return whole group", groupIdx, err)
		return group, nil
	}

	if strings.Contains(response, "DISCARD_ALL") {
		log.Printf("Filter Group %d: all discard", groupIdx)
		return []CodeChunk{}, nil
	}

	// Parse indices
	indexStrs := strings.Split(response, "|")
	var result []CodeChunk

	for _, indexStr := range indexStrs {
		index, err := strconv.Atoi(strings.TrimSpace(indexStr))
		if err != nil || index < 0 || index >= len(group) {
			log.Printf("Invalid index %s in group %d", indexStr, groupIdx)
			continue
		}
		result = append(result, group[index])
		log.Printf("Filter Group %d: save %s", groupIdx, group[index].RelativePath)
	}

	if len(result) == 0 {
		log.Printf("Filter Group %d: no valid results, return whole group", groupIdx)
		return group, nil
	}

	return result, nil
}

// FilterOrRank filters or ranks code chunks using concurrent processing
func (cr *CodebaseRetriever) FilterOrRank(ctx context.Context, query string, codeChunks []CodeChunk, promptTemplate *template.Template, groupingMethod string, maxContextSize, groupSize int) ([]CodeChunk, error) {
	var groups [][]CodeChunk

	if groupingMethod == "token" {
		groups = cr.GroupByTokens(codeChunks, maxContextSize)
		log.Printf("Token-based grouping: %v", cr.getGroupSizes(groups))
	} else {
		log.Printf("Fixed-size grouping: %d", groupSize)
		groups = cr.GroupByFixedSize(codeChunks, groupSize)
	}

	// Process groups concurrently
	results := make([][]CodeChunk, len(groups))
	errors := make([]error, len(groups))
	var wg sync.WaitGroup

	for i, group := range groups {
		wg.Add(1)
		go func(idx int, g []CodeChunk) {
			defer wg.Done()
			result, err := cr.AsyncMQuery(ctx, query, idx, g, promptTemplate)
			results[idx] = result
			errors[idx] = err
		}(i, group)
	}

	wg.Wait()

	// Check for errors
	for i, err := range errors {
		if err != nil {
			log.Printf("Error processing group %d: %v", i, err)
		}
	}

	// Flatten results
	var finalResult []CodeChunk
	for _, result := range results {
		finalResult = append(finalResult, result...)
	}

	return finalResult, nil
}

// BuildSearchResponse builds the final search response
func (cr *CodebaseRetriever) BuildSearchResponse(filterChunks []CodeChunk) string {
	var searchItems [][]string
	for _, chunk := range filterChunks {
		searchItems = append(searchItems, []string{
			chunk.RelativePath,
			chunk.Snippet,
		})
	}

	var responseBuf bytes.Buffer
	templateData := map[string]interface{}{
		"CodebaseRecallResult": searchItems,
	}

	if err := cr.responseTemplate.Execute(&responseBuf, templateData); err != nil {
		log.Printf("Error executing response template: %v", err)
		// Fallback to simple format
		var builder strings.Builder
		builder.WriteString("The following code sections were retrieved:\n\n")
		for _, chunk := range filterChunks {
			builder.WriteString(fmt.Sprintf("Path: %s\n%s\n\n", chunk.RelativePath, chunk.Snippet))
		}
		return builder.String()
	}

	response := responseBuf.String()

	// Truncate if too long
	maxSize := 20000
	if envSize := os.Getenv("SEARCH_RESPONSE_TRUNCATION_SIZE"); envSize != "" {
		if size, err := strconv.Atoi(envSize); err == nil {
			maxSize = size
		}
	}

	if len(response) > maxSize {
		response = response[:maxSize] + "..."
	}

	return response
}

// getGroupSizes returns the sizes of groups for logging
func (cr *CodebaseRetriever) getGroupSizes(groups [][]CodeChunk) []int {
	sizes := make([]int, len(groups))
	for i, group := range groups {
		sizes[i] = len(group)
	}
	return sizes
}

// Recall performs the actual API call to retrieve code chunks
func (cr *CodebaseRetriever) Recall(ctx context.Context, query, repoURL, branch string) (*RecallResponse, error) {
	// Use the configured API endpoint from config
	url := cr.config.RemoteRetrieval.URL

	// Convert git SSH URL to HTTP URL (same logic as in index_build.go)
	convertedRepoURL := repoURL
	if strings.HasPrefix(repoURL, "*******************:") {
		convertedRepoURL = strings.Replace(repoURL, "*******************:", "http://code.alipay.com/", 1)
		log.Printf("Converted repo URL: %s -> %s", repoURL, convertedRepoURL)
	}

	requestData := RecallRequest{
		RepoURL: convertedRepoURL,
		Branch:  branch,
		Query:   query,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	log.Printf("Request data: %s", string(jsonData))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers from config
	for key, value := range cr.config.RemoteRetrieval.Headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search failed: %d - %s", resp.StatusCode, string(body))
	}

	var recallResponse RecallResponse
	if err := json.Unmarshal(body, &recallResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	log.Printf("Response: Success=%v, ErrorType=%s, ErrorCode=%v, ErrorMsg=%s, DataChunks=%d",
		recallResponse.Success, recallResponse.ErrorType, recallResponse.ErrorCode,
		recallResponse.ErrorMsg, len(recallResponse.Data.CodeChunkWithCompleteSchema))

	// Check for success - either Success=true or ErrorCode="SUCCESS"
	if !recallResponse.Success && recallResponse.ErrorCode != "SUCCESS" {
		return nil, fmt.Errorf("search failed: %s", recallResponse.ErrorMsg)
	}

	return &recallResponse, nil
}

// Search performs the main search logic
func (cr *CodebaseRetriever) Search(ctx context.Context, query, repoURL, branch string) (string, error) {
	// Recall code chunks
	recallResult, err := cr.Recall(ctx, query, repoURL, branch)
	if err != nil {
		return "", fmt.Errorf("recall failed: %v", err)
	}

	recallChunks := recallResult.Data.CodeChunkWithCompleteSchema
	log.Printf("Recall %d Chunks", len(recallChunks))

	if len(recallChunks) == 0 {
		return "No relevant code found in the codebase", nil
	}

	// Sort chunks by file path and start line
	sort.Slice(recallChunks, func(i, j int) bool {
		if recallChunks[i].RelativePath != recallChunks[j].RelativePath {
			return recallChunks[i].RelativePath < recallChunks[j].RelativePath
		}
		return recallChunks[i].StartLine < recallChunks[j].StartLine
	})

	// Filter chunks
	filterChunks, err := cr.FilterOrRank(ctx, query, recallChunks, cr.filterTemplate, "fix", 30000, 5)
	if err != nil {
		return "", fmt.Errorf("filter failed: %v", err)
	}

	log.Printf("Filter %d Chunks", len(filterChunks))
	for _, chunk := range filterChunks {
		log.Printf("Filter Chunk Path: %s", chunk.RelativePath)
	}

	if len(filterChunks) == 0 {
		return "No relevant code found in the codebase", nil
	}

	// Group chunks by file
	filesToChunks := make(map[string][]CodeChunk)
	for _, chunk := range filterChunks {
		filePath := chunk.RelativePath
		filesToChunks[filePath] = append(filesToChunks[filePath], chunk)
	}

	// Merge chunks by file
	var fileMergedChunks []CodeChunk
	for filePath, chunks := range filesToChunks {
		log.Printf("Merge %d Chunks into one: %s", len(chunks), filePath)

		// Sort chunks by start line
		sort.Slice(chunks, func(i, j int) bool {
			return chunks[i].StartLine < chunks[j].StartLine
		})

		// Merge chunks
		var snippets []string
		for _, chunk := range chunks {
			snippets = append(snippets, chunk.Snippet)
		}

		mergedChunk := CodeChunk{
			RelativePath: filePath,
			Snippet:      strings.Join(snippets, "\n...\n"),
			StartLine:    chunks[0].StartLine,
			EndLine:      chunks[len(chunks)-1].EndLine,
		}
		fileMergedChunks = append(fileMergedChunks, mergedChunk)
	}

	// Rank chunks (skip if only one chunk)
	var rankChunks []CodeChunk
	if len(fileMergedChunks) == 1 {
		rankChunks = fileMergedChunks
	} else {
		rankChunks, err = cr.FilterOrRank(ctx, query, fileMergedChunks, cr.rankTemplate, "fix", 30000, len(fileMergedChunks))
		if err != nil {
			return "", fmt.Errorf("rank failed: %v", err)
		}
	}

	log.Printf("Rank %d Chunks", len(rankChunks))
	for _, chunk := range rankChunks {
		log.Printf("Rank Chunk Path: %s", chunk.RelativePath)
	}

	// Build final response
	searchResponse := cr.BuildSearchResponse(rankChunks)
	return searchResponse, nil
}

// GetFilterTemplate returns the filter template for testing
func (cr *CodebaseRetriever) GetFilterTemplate() *template.Template {
	return cr.filterTemplate
}

// GetRankTemplate returns the rank template for testing
func (cr *CodebaseRetriever) GetRankTemplate() *template.Template {
	return cr.rankTemplate
}

// Retrieve is the main entry point for codebase retrieval
func (cr *CodebaseRetriever) Retrieve(ctx context.Context, informationRequest, remoteURL, remoteBranch string) (string, error) {
	log.Printf("Executing codebase retrieval: %s", informationRequest)
	searchResponse, err := cr.Search(ctx, informationRequest, remoteURL, remoteBranch)
	if err != nil {
		return "", err
	}
	return searchResponse, nil
}
