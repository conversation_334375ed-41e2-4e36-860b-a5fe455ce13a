package augment

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"text/template"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"
	"codefuse-cli/utils"
)

// RetrivalType 检索类型枚举
type RetrivalType string

const (
	RetrivalTypeDefinition RetrivalType = "definition"
	RetrivalTypeInvocation RetrivalType = "invocation"
)

// SymbolResult 符号搜索结果
type SymbolResult struct {
	Content   string                 `json:"content"`
	FilePath  string                 `json:"file_path"`
	StartLine int                    `json:"start_line"`
	ExtraData map[string]interface{} `json:"extra_data"`
}

// APIRequest 新的API请求结构
type APIRequest struct {
	RepoURL       string       `json:"repoURL"`
	Branch        string       `json:"branch"`
	RetrievalType RetrivalType `json:"retrievalType"`
	MethodName    string       `json:"methodName"`
	ClassName     string       `json:"className"`
}

// Metadata 响应元数据
type Metadata struct {
	RepoURL  string `json:"repo_url"`
	Commit   string `json:"commit"`
	RepoName string `json:"repo_name"`
	Branch   string `json:"branch"`
}

// APIResponseData 单个响应数据项
type APIResponseData struct {
	Metadata Metadata                   `json:"metadata"`
	Data     []map[string]interface{}   `json:"data"`
}

// RemoteSymbolRetriever 远程符号检索器
type RemoteSymbolRetriever struct {
	config *utils.Config
	client *http.Client
}

// NewRemoteSymbolRetriever 创建新的远程符号检索器
func NewRemoteSymbolRetriever() *RemoteSymbolRetriever {
	return NewRemoteSymbolRetrieverWithConfig(nil)
}

// NewRemoteSymbolRetrieverWithConfig 创建带配置的远程符号检索器
func NewRemoteSymbolRetrieverWithConfig(config *utils.Config) *RemoteSymbolRetriever {
	// 如果没有提供配置，加载默认配置
	if config == nil {
		var err error
		config, err = utils.LoadConfig()
		if err != nil {
			// 如果加载配置失败，使用默认配置
			config = utils.DefaultConfig()
		}
	}

	return &RemoteSymbolRetriever{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// makeRequest 发送HTTP请求并处理响应
func (r *RemoteSymbolRetriever) makeRequest(ctx context.Context, request APIRequest) ([]SymbolResult, error) {
	// 使用配置中的接口地址
	apiURL := r.config.SymbolRetrieval.BaseURL
	
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 只需要设置Content-Type，不需要鉴权头
	req.Header.Set("Content-Type", "application/json")

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// 解析响应
	var results []SymbolResult
	
	// 先尝试解析为单个响应对象（definition）
	var singleResp APIResponseData
	if err := json.Unmarshal(body, &singleResp); err == nil {
		// 转换数据格式
		for _, item := range singleResp.Data {
			result := SymbolResult{
				Content:   getString(item, "content"),
				FilePath:  getString(item, "file_path"),
				StartLine: getInt(item, "start_line"),
			}
			if extraData, ok := item["extra_data"].(map[string]interface{}); ok {
				result.ExtraData = extraData
			}
			results = append(results, result)
		}
		return results, nil
	}

	// 尝试解析为数组响应（invocation）
	var arrayResp []APIResponseData
	if err := json.Unmarshal(body, &arrayResp); err == nil {
		for _, respItem := range arrayResp {
			for _, item := range respItem.Data {
				result := SymbolResult{
					Content:   getString(item, "content"),
					FilePath:  getString(item, "file_path"),
					StartLine: getInt(item, "start_line"),
				}
				if extraData, ok := item["extra_data"].(map[string]interface{}); ok {
					result.ExtraData = extraData
				}
				results = append(results, result)
			}
		}
		return results, nil
	}

	return nil, fmt.Errorf("failed to parse response: %s", string(body))
}

// SymbolSearch 符号搜索统一接口
func (r *RemoteSymbolRetriever) SymbolSearch(ctx context.Context, repoURL, branch string, retrivalType RetrivalType, methodName, className string) (string, error) {
	request := APIRequest{
		RepoURL:       repoURL,
		Branch:        branch,
		RetrievalType: retrivalType,
		MethodName:    methodName,
		ClassName:     className,
	}

	results, err := r.makeRequest(ctx, request)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %v", err)
	}

	// 构建标题
	var title string
	if len(results) > 0 {
		if retrivalType == RetrivalTypeDefinition {
			title = "Here is the result of symbol definition of "
		} else {
			title = "Here is the result of symbol invocation of "
		}
	} else {
		if retrivalType == RetrivalTypeDefinition {
			title = "No symbol definition found of "
		} else {
			title = "No symbol invocation found of "
		}
	}

	if className != "" {
		title += fmt.Sprintf("%s.%s", className, methodName)
	} else {
		title += methodName
	}

	// 转换为通用格式
	var formattedResults []interface{}
	for _, result := range results {
		formattedResults = append(formattedResults, map[string]interface{}{
			"file_path":  result.FilePath,
			"content":    result.Content,
			"start_line": result.StartLine,
		})
	}

	return formatSymbolSearchResult(title, formattedResults), nil
}

// formatSymbolSearchResult 格式化符号搜索结果
func formatSymbolSearchResult(title string, result []interface{}) string {
	templateStr := `{{ .Title }}
{{ range .Result }}
Path: {{ .FilePath }}{{ if .StartLine }} (Line {{ .StartLine }}){{ end }}
...
{{ .Content }}
... 

{{ end }}`

	tmpl, err := template.New("symbol_search").Parse(templateStr)
	if err != nil {
		return fmt.Sprintf("Template error: %v", err)
	}

	var formattedResult []map[string]interface{}
	for _, item := range result {
		if itemMap, ok := item.(map[string]interface{}); ok {
			formatted := map[string]interface{}{
				"FilePath": getString(itemMap, "file_path"),
				"Content":  getString(itemMap, "content"),
			}
			if startLine := getInt(itemMap, "start_line"); startLine > 0 {
				formatted["StartLine"] = startLine
			}
			formattedResult = append(formattedResult, formatted)
		}
	}

	data := map[string]interface{}{
		"Title":  title,
		"Result": formattedResult,
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return fmt.Sprintf("Template execution error: %v", err)
	}

	return buf.String()
}

// getString 安全地从map中获取字符串值
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// getInt 安全地从map中获取整数值
func getInt(m map[string]interface{}, key string) int {
	if val, ok := m[key]; ok {
		if num, ok := val.(float64); ok {
			return int(num)
		}
		if num, ok := val.(int); ok {
			return num
		}
	}
	return 0
}

// NewSymbolRetrieverTool 创建新的符号检索工具
func NewSymbolRetrieverTool() *SymbolRetrieverTool {
	return &SymbolRetrieverTool{
		retriever: NewRemoteSymbolRetriever(),
	}
}

// NewSymbolRetrieverToolWithConfig 创建带配置的符号检索工具
func NewSymbolRetrieverToolWithConfig(config *utils.Config) *SymbolRetrieverTool {
	return &SymbolRetrieverTool{
		retriever: NewRemoteSymbolRetrieverWithConfig(config),
	}
}

// SymbolRetrieverTool 符号检索工具
type SymbolRetrieverTool struct {
	retriever *RemoteSymbolRetriever
}

// GetDefinition 获取工具定义
func (st *SymbolRetrieverTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "symbol_retrieval",
			Description: "Search for symbol definitions or usages in the codebase. Use 'definition' to find where a method is implemented, or 'invocation' to find where it's called.",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"repo_url": map[string]interface{}{
						"type":        "string",
						"description": "Repository URL (e.g., https://code.alipay.com/common_release/codegencore.git)",
					},
					"branch": map[string]interface{}{
						"type":        "string",
						"description": "Branch name to search in (e.g., master, main)",
					},
					"retrival_type": map[string]interface{}{
						"type":        "string",
						"description": "'definition' to find where a symbol is defined, 'invocation' to find where it's called",
						"enum":        []string{"definition", "invocation"},
					},
					"method_name": map[string]interface{}{
						"type":        "string",
						"description": "Method/function name to search for (use simple name like 'processData')",
					},
					"class_name": map[string]interface{}{
						"type":        "string",
						"description": "Class name containing the method (optional, improves accuracy)",
					},
				},
				"required": []string{"repo_url", "branch", "retrival_type", "method_name"},
			},
		},
	}
}

// SymbolRetrieverArgs 符号检索参数
type SymbolRetrieverArgs struct {
	RepoURL      string       `json:"repo_url"`
	Branch       string       `json:"branch"`
	RetrivalType RetrivalType `json:"retrival_type"`
	MethodName   string       `json:"method_name"`
	ClassName    string       `json:"class_name,omitempty"`
}

// Execute 执行符号检索
func (st *SymbolRetrieverTool) Execute(arguments string) (string, error) {
	var args SymbolRetrieverArgs
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证参数
	if args.RepoURL == "" {
		return "", fmt.Errorf("repo_url is required")
	}
	if args.Branch == "" {
		return "", fmt.Errorf("branch is required")
	}
	if args.MethodName == "" {
		return "", fmt.Errorf("method_name is required")
	}
	if args.RetrivalType != RetrivalTypeDefinition && args.RetrivalType != RetrivalTypeInvocation {
		return "", fmt.Errorf("retrival_type must be 'definition' or 'invocation'")
	}

	// 执行检索
	ctx := context.Background()
	result, err := st.retriever.SymbolSearch(ctx, args.RepoURL, args.Branch, args.RetrivalType, args.MethodName, args.ClassName)
	if err != nil {
		return "", fmt.Errorf("symbol search failed: %v", err)
	}

	return result, nil
}

