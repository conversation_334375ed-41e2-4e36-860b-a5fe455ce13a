package augment

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/sasha<PERSON><PERSON>/go-openai"
)

const (
	bashTimeout = 120 * time.Second
)

// BashTool 实现bash命令执行工具
type BashTool struct {
	process *exec.Cmd
}

// NewBashTool 创建新的bash工具实例
func NewBashTool() *BashTool {
	return &BashTool{}
}

// GetDefinition 获取工具定义
func (bt *BashTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "bash",
			Description: "Execute a bash command in the terminal.\n* Long running commands: For commands that may run indefinitely, it should be run in the background and the output should be redirected to a file, e.g. command = `python3 app.py > server.log 2>&1 &`.\n* Interactive: If a bash command returns exit code `-1`, this means the process is not yet finished. The assistant must then send a second call to terminal with an empty `command` (which will retrieve any additional logs), or it can send additional text (set `command` to the text) to STDIN of the running process, or it can send command=`ctrl+c` to interrupt the process.\n* Timeout: If a command execution result says \"Command timed out. Sending SIGINT to the process\", the assistant should retry running the command in the background.",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"command": map[string]interface{}{
						"type":        "string",
						"description": "The bash command to execute. Can be empty to view additional logs when previous exit code is `-1`. Can be `ctrl+c` to interrupt the currently running process.",
					},
				},
				"required": []string{"command"},
			},
		},
	}
}

// BashArgs 定义bash命令参数
type BashArgs struct {
	Command string `json:"command"`
}

// Execute 执行bash命令
func (bt *BashTool) Execute(arguments string) (string, error) {
	var args BashArgs
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 如果命令为空，返回错误
	if args.Command == "" {
		return "", fmt.Errorf("no command provided")
	}

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), bashTimeout)
	defer cancel()

	// 创建命令
	cmd := exec.CommandContext(ctx, "bash", "-c", args.Command)
	
	// 执行命令并获取输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			return "", fmt.Errorf("command timed out after %v", bashTimeout)
		}
		return "", fmt.Errorf("command execution failed: %v", err)
	}

	// 返回命令输出
	return strings.TrimSpace(string(output)), nil
}
