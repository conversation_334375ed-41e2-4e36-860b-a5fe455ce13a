package augment

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"
)

// Constants
const (
	MaxResponseLen      = 50000
	TruncatedMessage    = "<response clipped><NOTE>To save on context only part of this file has been shown to you.</NOTE>"
	SnippetContextLines = 4
)

// ToolResponse represents a tool response
type ToolResponse struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

// FileTextInfo contains file text and line ending info
type FileTextInfo struct {
	Content            string
	OriginalLineEnding string
}

// ViewRangeResult contains validated view range info
type ViewRangeResult struct {
	InitLine  int
	FinalLine int
	Message   string
}

// Match represents a string match in content
type Match struct {
	StartLine int
	EndLine   int
}

// Indentation represents indentation style
type Indentation struct {
	Type string // "tab" or "space"
	Size int
}

// StrReplaceEntry represents a string replacement entry
type StrReplaceEntry struct {
	Index                 int    `json:"index"`
	OldStr                string `json:"old_str"`
	NewStr                string `json:"new_str"`
	OldStrStartLineNumber *int   `json:"old_str_start_line_number"`
	OldStrEndLineNumber   *int   `json:"old_str_end_line_number"`
}

// InsertLineEntry represents a line insertion entry
type InsertLineEntry struct {
	Index      int    `json:"index"`
	InsertLine int    `json:"insert_line"`
	NewStr     string `json:"new_str"`
}

// EditResult represents the result of an edit operation
type EditResult struct {
	IsError               bool
	Index                 int
	OldStr                string
	OldStrStartLineNumber *int
	OldStrEndLineNumber   *int
	NewContent            string
	NewStr                string
	NewStrStartLineNumber *int
	NewStrEndLineNumber   *int
	NumLinesDiff          int
	GenMessageFunc        func(*EditResult) string
}

// StrReplaceEditorTool is the main tool for editing files
type StrReplaceEditorTool struct {
	WorkspaceRootPath        string
	LineNumberErrorTolerance float64
	WaitForAutoFormatMs      int
}

// NewStrReplaceEditorTool creates a new editor tool
func NewStrReplaceEditorTool(workspaceRootPath string) *StrReplaceEditorTool {
	return &StrReplaceEditorTool{
		WorkspaceRootPath:        workspaceRootPath,
		LineNumberErrorTolerance: 0.2,
		WaitForAutoFormatMs:      1000,
	}
}

// Utility functions

// successToolResponse creates a success tool response
func successToolResponse(message string) ToolResponse {
	return ToolResponse{Type: "success", Message: message}
}

// errorToolResponse creates an error tool response
func errorToolResponse(message string) ToolResponse {
	return ToolResponse{Type: "error", Message: message}
}

// removeTrailingWhitespace removes trailing whitespace from each line
func removeTrailingWhitespace(text string) string {
	lineEnding := "\n"
	if strings.Contains(text, "\r\n") {
		lineEnding = "\r\n"
	}

	lines := strings.Split(text, lineEnding)
	trimmedLines := make([]string, len(lines))
	for i, line := range lines {
		trimmedLines[i] = strings.TrimRight(line, " \t")
	}
	return strings.Join(trimmedLines, lineEnding)
}

// makeOutput formats file content with line numbers
func makeOutput(fileContent, fileDescriptor string, initLine int, totalLines *int) string {
	fileContent = maybeTruncate(fileContent, MaxResponseLen)
	lines := strings.Split(fileContent, "\n")

	numberedLines := make([]string, len(lines))
	for i, line := range lines {
		lineNum := strconv.Itoa(i + initLine)
		numberedLines[i] = fmt.Sprintf("%6s\t%s", lineNum, line)
	}

	output := fmt.Sprintf("Here's the result of running `cat -n` on %s:\n%s\n",
		fileDescriptor, strings.Join(numberedLines, "\n"))

	if totalLines != nil {
		output += fmt.Sprintf("Total lines in file: %d\n", *totalLines)
	}

	return output
}

// maybeTruncate truncates content if it's too long
func maybeTruncate(content string, truncateAfter int) string {
	if len(content) <= truncateAfter {
		return content
	}
	return content[:truncateAfter] + TruncatedMessage
}

// normalizeLineEndings normalizes line endings to '\n'
func normalizeLineEndings(text string) string {
	return strings.ReplaceAll(text, "\r\n", "\n")
}

// detectLineEnding detects line ending used in text
func detectLineEnding(text string) string {
	if strings.Contains(text, "\r\n") {
		return "\r\n"
	}
	return "\n"
}

// prepareTextForEditing prepares text for editing
func prepareTextForEditing(text string) FileTextInfo {
	originalLineEnding := detectLineEnding(text)
	content := normalizeLineEndings(removeTrailingWhitespace(text))
	return FileTextInfo{
		Content:            content,
		OriginalLineEnding: originalLineEnding,
	}
}

// restoreLineEndings restores original line endings
func restoreLineEndings(text, lineEnding string) string {
	if lineEnding == "\n" {
		return text
	}
	return strings.ReplaceAll(text, "\n", lineEnding)
}

// validateViewRange validates and adjusts the view range
func validateViewRange(viewRange []int, numLinesFile, minViewSize int) ViewRangeResult {
	message := ""

	if len(viewRange) != 2 {
		message = fmt.Sprintf("Invalid view range provided. Showing entire file (lines 1-%d).", numLinesFile)
		return ViewRangeResult{InitLine: 1, FinalLine: numLinesFile, Message: message}
	}

	initLine := viewRange[0]
	finalLine := viewRange[1]

	if initLine < 1 {
		message += fmt.Sprintf("Start line %d is less than 1. Adjusted to 1.\n", initLine)
		initLine = 1
	} else if initLine > numLinesFile {
		message += fmt.Sprintf("Start line %d exceeds file length (%d). Adjusted to 1.\n", initLine, numLinesFile)
		initLine = 1
	}

	if finalLine == -1 {
		finalLine = numLinesFile
	} else if finalLine > numLinesFile {
		message += fmt.Sprintf("End line %d exceeds file length (%d). Adjusted to %d. ", finalLine, numLinesFile, numLinesFile)
		finalLine = numLinesFile
	} else if finalLine < initLine {
		message += fmt.Sprintf("End line %d is less than start line %d. Adjusted to %d. ", finalLine, initLine, numLinesFile)
		finalLine = numLinesFile
	} else if finalLine-initLine+1 < minViewSize {
		finalLineExpanded := int(math.Min(float64(initLine+minViewSize-1), float64(numLinesFile)))
		message += fmt.Sprintf("View range expanded to meet minimum size of %d lines. ", minViewSize)

		if finalLineExpanded >= numLinesFile {
			message += fmt.Sprintf("End line adjusted to last line of file (%d).", numLinesFile)
		} else {
			message += fmt.Sprintf("New range: [%d, %d].", initLine, finalLineExpanded)
		}

		finalLine = finalLineExpanded
	}

	return ViewRangeResult{
		InitLine:  initLine,
		FinalLine: finalLine,
		Message:   strings.TrimSpace(message),
	}
}

// createSnippetStr creates a formatted snippet with line numbers
func createSnippetStr(content string, replacementStartLine, replacementNumLines, snippetContextLines int) string {
	startLine := int(math.Max(0, float64(replacementStartLine-snippetContextLines)))
	endLine := replacementStartLine + replacementNumLines - 1 + snippetContextLines

	content = normalizeLineEndings(content)
	lines := strings.Split(content, "\n")

	if endLine >= len(lines) {
		endLine = len(lines) - 1
	}

	// Ensure startLine doesn't exceed endLine after adjustment
	if startLine > endLine {
		startLine = endLine
	}

	snippet := lines[startLine : endLine+1]

	numberedLines := make([]string, len(snippet))
	for i, line := range snippet {
		lineNum := strconv.Itoa(i + startLine + 1)
		numberedLines[i] = fmt.Sprintf("%6s\t%s", lineNum, line)
	}

	return strings.Join(numberedLines, "\n")
}

// detectIndentation detects the indentation style used in content
func detectIndentation(content string) Indentation {
	lines := strings.Split(content, "\n")
	spaceIndents := 0
	tabIndents := 0
	spaceSize := 0

	spacePattern := regexp.MustCompile(`^( +)`)
	tabPattern := regexp.MustCompile(`^(\t+)`)

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		if spaceMatch := spacePattern.FindStringSubmatch(line); spaceMatch != nil {
			spaceIndents++
			if spaceSize == 0 {
				spaceSize = len(spaceMatch[1])
			}
		} else if tabPattern.MatchString(line) {
			tabIndents++
		}
	}

	if tabIndents > spaceIndents {
		return Indentation{Type: "tab", Size: 1}
	}

	if spaceSize == 0 {
		spaceSize = 2
	}

	return Indentation{Type: "space", Size: spaceSize}
}

// removeOneIndentLevel removes one level of indentation from text
func removeOneIndentLevel(text string, indentation Indentation) string {
	lines := strings.Split(text, "\n")

	var pattern *regexp.Regexp
	if indentation.Type == "tab" {
		pattern = regexp.MustCompile(`^\t`)
	} else {
		pattern = regexp.MustCompile(fmt.Sprintf(`^ {1,%d}`, indentation.Size))
	}

	result := make([]string, len(lines))
	for i, line := range lines {
		result[i] = pattern.ReplaceAllString(line, "")
	}

	return strings.Join(result, "\n")
}

// allLinesHaveIndent checks if all lines have at least one level of indentation
func allLinesHaveIndent(text string, indentation Indentation) bool {
	lines := strings.Split(text, "\n")

	var pattern *regexp.Regexp
	if indentation.Type == "tab" {
		pattern = regexp.MustCompile(`^\t`)
	} else {
		pattern = regexp.MustCompile(`^ +`)
	}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		if !pattern.MatchString(line) {
			return false
		}
	}

	return true
}

// findMatches finds all occurrences of a string in content
func findMatches(content, strToFind string) []Match {
	contentLines := strings.Split(content, "\n")
	strLines := strings.Split(strToFind, "\n")
	var matches []Match

	if strings.TrimSpace(strToFind) == "" || len(strLines) > len(contentLines) {
		return matches
	}

	// For single-line search
	if len(strLines) == 1 {
		for index, line := range contentLines {
			if strings.Contains(line, strToFind) {
				matches = append(matches, Match{
					StartLine: index,
					EndLine:   index,
				})
			}
		}
		return matches
	}

	// For multi-line search
	contentText := content
	searchText := strToFind
	startIndex := 0

	for {
		foundIndex := strings.Index(contentText[startIndex:], searchText)
		if foundIndex == -1 {
			break
		}

		foundIndex += startIndex
		textBeforeMatch := contentText[:foundIndex]
		textUpToEndOfMatch := contentText[:foundIndex+len(searchText)]

		startLine := strings.Count(textBeforeMatch, "\n")
		endLine := strings.Count(textUpToEndOfMatch, "\n")

		matches = append(matches, Match{
			StartLine: startLine,
			EndLine:   endLine,
		})

		startIndex = foundIndex + 1
	}

	return matches
}

// findClosestMatch finds the closest match to the target line range
func findClosestMatch(matches []Match, targetStartLine, targetEndLine int, lineNumberErrorTolerance float64) int {
	if len(matches) == 0 {
		return -1
	}

	if len(matches) == 1 {
		return 0
	}

	// Exact match
	for i, match := range matches {
		if match.StartLine == targetStartLine && match.EndLine == targetEndLine {
			return i
		}
	}

	if lineNumberErrorTolerance == 0 {
		return -1
	}

	// Find closest match
	closestIndex := -1
	minDistance := math.Inf(1)

	for i, match := range matches {
		distance := math.Abs(float64(match.StartLine - targetStartLine))
		if distance < minDistance {
			minDistance = distance
			closestIndex = i
		}
	}

	if lineNumberErrorTolerance == 1 {
		return closestIndex
	}

	if closestIndex == -1 {
		return -1
	}

	// Check if there's another close match
	nextClosestDistance := math.Inf(1)
	nextClosestIndex := -1

	for i, match := range matches {
		if i == closestIndex {
			continue
		}

		distance := math.Abs(float64(match.StartLine - targetStartLine))
		if distance < nextClosestDistance {
			nextClosestDistance = distance
			nextClosestIndex = i
		}
	}

	if nextClosestIndex != -1 {
		// Calculate tolerance threshold
		distanceBetweenMatches := math.Abs(float64(matches[nextClosestIndex].StartLine - matches[closestIndex].StartLine))
		toleranceThreshold := int((distanceBetweenMatches / 2) * lineNumberErrorTolerance)

		if minDistance <= float64(toleranceThreshold) {
			return closestIndex
		}
	}

	return -1
}

// validateStrReplaceEntries validates string replacement entries
func validateStrReplaceEntries(entries []map[string]interface{}) ([]StrReplaceEntry, error) {
	if len(entries) == 0 {
		return nil, fmt.Errorf("empty required parameter str_replace_entries")
	}

	var validated []StrReplaceEntry

	for i, entry := range entries {
		oldStr, ok := entry["old_str"].(string)
		if !ok {
			return nil, fmt.Errorf("missing required parameter old_str for entry %d", i)
		}

		newStr, ok := entry["new_str"].(string)
		if !ok {
			return nil, fmt.Errorf("missing required parameter new_str for entry %d", i)
		}

		var oldStrStartLineNumber, oldStrEndLineNumber *int

		if startLine, exists := entry["old_str_start_line_number"]; exists {
			if startLineFloat, ok := startLine.(float64); ok && startLineFloat != 0 {
				startLineInt := int(startLineFloat)
				if startLineInt < 1 {
					return nil, fmt.Errorf("invalid old_str_start_line_number for entry %d: must be positive", i)
				}
				oldStrStartLineNumber = &startLineInt
			}
		}

		if endLine, exists := entry["old_str_end_line_number"]; exists {
			if endLineFloat, ok := endLine.(float64); ok && endLineFloat != 0 {
				endLineInt := int(endLineFloat)
				if endLineInt < 1 {
					return nil, fmt.Errorf("invalid old_str_end_line_number for entry %d: must be positive", i)
				}
				oldStrEndLineNumber = &endLineInt
			}
		}

		index := i
		if idx, exists := entry["index"]; exists {
			if idxFloat, ok := idx.(float64); ok {
				index = int(idxFloat)
			}
		}

		validated = append(validated, StrReplaceEntry{
			Index:                 index,
			OldStr:                oldStr,
			NewStr:                newStr,
			OldStrStartLineNumber: oldStrStartLineNumber,
			OldStrEndLineNumber:   oldStrEndLineNumber,
		})
	}

	return validated, nil
}

// prepareStrReplaceEntries prepares string replacement entries for processing
func prepareStrReplaceEntries(entries []StrReplaceEntry) []StrReplaceEntry {
	// Convert to zero-based line numbers
	prepared := toZeroBased(entries)

	// Sort entries in reverse order by line number to avoid index issues
	sort.Slice(prepared, func(i, j int) bool {
		startI := -1
		startJ := -1

		if prepared[i].OldStrStartLineNumber != nil {
			startI = *prepared[i].OldStrStartLineNumber
		}

		if prepared[j].OldStrStartLineNumber != nil {
			startJ = *prepared[j].OldStrStartLineNumber
		}

		return startI > startJ
	})

	return prepared
}

// toZeroBased converts 1-based line numbers to 0-based
func toZeroBased(entries []StrReplaceEntry) []StrReplaceEntry {
	result := make([]StrReplaceEntry, len(entries))

	for i, entry := range entries {
		result[i] = entry

		if entry.OldStrStartLineNumber != nil {
			newStart := *entry.OldStrStartLineNumber - 1
			result[i].OldStrStartLineNumber = &newStart
		}

		if entry.OldStrEndLineNumber != nil {
			newEnd := *entry.OldStrEndLineNumber - 1
			result[i].OldStrEndLineNumber = &newEnd
		}
	}

	return result
}

// findOverlappingEntry finds any entry with overlapping line numbers
func findOverlappingEntry(currentEntry StrReplaceEntry, entries []StrReplaceEntry) *StrReplaceEntry {
	if currentEntry.OldStrStartLineNumber == nil || currentEntry.OldStrEndLineNumber == nil {
		return nil
	}

	startLine := *currentEntry.OldStrStartLineNumber
	endLine := *currentEntry.OldStrEndLineNumber

	for _, entry := range entries {
		if currentEntry.Index == entry.Index ||
			entry.OldStrStartLineNumber == nil ||
			entry.OldStrEndLineNumber == nil {
			continue
		}

		rangeStartLine := *entry.OldStrStartLineNumber
		rangeEndLine := *entry.OldStrEndLineNumber

		// Check for overlap
		if (startLine <= rangeStartLine && rangeStartLine <= endLine) ||
			(startLine <= rangeEndLine && rangeEndLine <= endLine) ||
			(rangeStartLine <= startLine && startLine <= rangeEndLine) {
			return &entry
		}
	}

	return nil
}

// genOverlappingEntryErrorMessage generates error message for overlapping entries
func genOverlappingEntryErrorMessage(currentEntry StrReplaceEntry, overlappingEntry StrReplaceEntry) string {
	curStart := *currentEntry.OldStrStartLineNumber + 1
	curEnd := *currentEntry.OldStrEndLineNumber + 1
	overlapStart := *overlappingEntry.OldStrStartLineNumber + 1
	overlapEnd := *overlappingEntry.OldStrEndLineNumber + 1

	return fmt.Sprintf(`old_str line numbers range overlaps with another entry.
This entry range: [%d-%d]
Overlapping entry index: %d
Overlapping entry range: [%d-%d]`, curStart, curEnd, overlappingEntry.Index, overlapStart, overlapEnd)
}

// ReadFile reads the contents of a file
func (tool *StrReplaceEditorTool) ReadFile(path string) (string, error) {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return "", fmt.Errorf("error reading file %s: %v", path, err)
	}
	return string(content), nil
}

// HandleView handles the view command
func (tool *StrReplaceEditorTool) HandleView(path, fileContents string, viewRange []int) string {
	log.Printf("Handling view command for %s with range: %v", path, viewRange)

	fullPath := filepath.Join(tool.WorkspaceRootPath, path)
	log.Printf("Convert to absolute path: %s", fullPath)

	fileContents = normalizeLineEndings(fileContents)
	fileLines := strings.Split(fileContents, "\n")
	numLinesFile := len(fileLines)

	log.Printf("File %s has %d lines", path, numLinesFile)

	initLine := 1
	finalLine := numLinesFile
	rangeMessage := ""

	if len(viewRange) > 0 {
		minViewSize := 500
		validRange := validateViewRange(viewRange, numLinesFile, minViewSize)
		initLine = validRange.InitLine
		finalLine = validRange.FinalLine

		if validRange.Message != "" {
			rangeMessage = fmt.Sprintf("Note:\n%s\n\n", validRange.Message)
		}
	}

	var slicedContents []string
	if finalLine != -1 {
		slicedContents = fileLines[initLine-1 : finalLine]
	} else {
		slicedContents = fileLines[initLine-1:]
	}

	slicedContentsStr := strings.Join(slicedContents, "\n")
	totalLines := &numLinesFile
	output := makeOutput(slicedContentsStr, path, initLine, totalLines)

	return rangeMessage + output
}

// singleStrReplace performs a single string replacement
func (tool *StrReplaceEditorTool) singleStrReplace(path, content, oldStr, newStr string, index int, oldStrStartLineNumber, oldStrEndLineNumber *int) EditResult {
	// Prepare strings
	oldStrInfo := prepareTextForEditing(oldStr)
	oldStr = oldStrInfo.Content

	newStrInfo := prepareTextForEditing(newStr)
	newStr = newStrInfo.Content

	// Helper function to create success result
	makeSuccessResult := func(newContent, newStr string, newStrStartLine, newStrEndLine, numLinesDiff int) EditResult {
		newStrStart := &newStrStartLine
		newStrEnd := &newStrEndLine

		return EditResult{
			IsError:               false,
			Index:                 index,
			OldStr:                oldStr,
			OldStrStartLineNumber: oldStrStartLineNumber,
			OldStrEndLineNumber:   oldStrEndLineNumber,
			NewContent:            newContent,
			NewStr:                newStr,
			NewStrStartLineNumber: newStrStart,
			NewStrEndLineNumber:   newStrEnd,
			NumLinesDiff:          numLinesDiff,
			GenMessageFunc: func(result *EditResult) string {
				snippetStr := createSnippetStr(
					result.NewContent,
					*result.NewStrStartLineNumber,
					*result.NewStrEndLineNumber-*result.NewStrStartLineNumber+1,
					SnippetContextLines,
				)
				return fmt.Sprintf("Replacement successful.\nEdited section after IDE auto-formatting was applied:\n%s", snippetStr)
			},
		}
	}

	// Helper function to create error result
	makeErrorResult := func(messageFunc func(*EditResult) string) EditResult {
		return EditResult{
			IsError:               true,
			Index:                 index,
			OldStr:                oldStr,
			OldStrStartLineNumber: oldStrStartLineNumber,
			OldStrEndLineNumber:   oldStrEndLineNumber,
			NumLinesDiff:          0,
			GenMessageFunc:        messageFunc,
		}
	}

	// Empty old_str is only allowed for empty files
	if strings.TrimSpace(oldStr) == "" {
		if strings.TrimSpace(content) == "" {
			newContent := newStr
			newStrStartLine := 0
			newStrEndLine := len(strings.Split(newStr, "\n")) - 1
			numLinesDiff := len(strings.Split(newStr, "\n"))
			return makeSuccessResult(newContent, newStr, newStrStartLine, newStrEndLine, numLinesDiff)
		} else {
			return makeErrorResult(func(result *EditResult) string {
				return fmt.Sprintf("No replacement was performed, old_str is empty which is only allowed when the file is empty or contains only whitespace. The file %s is not empty.", path)
			})
		}
	}

	// Find matches for the string to replace
	matches := findMatches(content, oldStr)

	if len(matches) == 0 {
		// Try with indent detection as fallback
		contentIndentation := detectIndentation(content)
		oldStrIndentation := detectIndentation(oldStr)
		newStrIndentation := detectIndentation(newStr)

		if contentIndentation.Type == "tab" &&
			oldStrIndentation.Type == "tab" &&
			newStrIndentation.Type == "tab" &&
			allLinesHaveIndent(oldStr, contentIndentation) &&
			allLinesHaveIndent(newStr, contentIndentation) {

			currentOldStr := removeOneIndentLevel(oldStr, contentIndentation)
			currentNewStr := removeOneIndentLevel(newStr, contentIndentation)

			matches = findMatches(content, currentOldStr)
			if len(matches) > 0 {
				oldStr = currentOldStr
				newStr = currentNewStr
			}
		}

		// If still no matches, return error
		if len(matches) == 0 {
			return makeErrorResult(func(result *EditResult) string {
				message := fmt.Sprintf("No replacement was performed, oldStr did not appear verbatim in %s.", path)
				if result.OldStrStartLineNumber != nil && result.OldStrEndLineNumber != nil {
					oldStrRegionSnippet := createSnippetStr(
						content,
						*result.OldStrStartLineNumber,
						*result.OldStrEndLineNumber-*result.OldStrStartLineNumber+1,
						SnippetContextLines,
					)

					lines := strings.Split(content, "\n")
					regionContent := strings.Join(lines[*result.OldStrStartLineNumber:*result.OldStrEndLineNumber+1], "\n")

					message += fmt.Sprintf("\nThe content in the specified region is:\n%s\n\nExpected oldStr vs actual content differ.", oldStrRegionSnippet)
					_ = regionContent // Could add diff here if needed
				}
				return message
			})
		}
	}

	var newContent string
	var newStrStartLine, newStrEndLine int

	// Handle single match
	if len(matches) == 1 {
		match := matches[0]
		newContent = strings.Replace(content, oldStr, newStr, 1)
		newStrStartLine = match.StartLine
		newStrEndLine = match.StartLine + len(strings.Split(newStr, "\n")) - 1
	} else {
		// Need line numbers to disambiguate multiple matches
		if oldStrStartLineNumber == nil || oldStrEndLineNumber == nil {
			return makeErrorResult(func(result *EditResult) string {
				return fmt.Sprintf("Multiple occurrences of oldStr `%s` found. Please provide line numbers to disambiguate.", oldStr)
			})
		}

		// Find closest match to provided line numbers
		matchIndex := findClosestMatch(matches, *oldStrStartLineNumber, *oldStrEndLineNumber, tool.LineNumberErrorTolerance)

		if matchIndex == -1 {
			startLine := *oldStrStartLineNumber + 1 // Convert to 1-based for error message
			endLine := *oldStrEndLineNumber + 1
			return makeErrorResult(func(result *EditResult) string {
				return fmt.Sprintf("No match found close to the provided line numbers (%d, %d).", startLine, endLine)
			})
		}

		match := matches[matchIndex]
		contentLines := strings.Split(content, "\n")

		// Get content before, at, and after the match
		contentBeforeMatch := strings.Join(contentLines[:match.StartLine], "\n")
		contentAfterMatch := strings.Join(contentLines[match.EndLine+1:], "\n")
		matchLines := strings.Join(contentLines[match.StartLine:match.EndLine+1], "\n")

		// Find the exact position of the match
		matchPosition := strings.Index(matchLines, oldStr)
		if matchPosition == -1 {
			return makeErrorResult(func(result *EditResult) string {
				return "Internal error: Could not find the exact position of the match."
			})
		}

		// Rebuild the content with the replacement
		beforeMatch := matchLines[:matchPosition]
		afterMatch := matchLines[matchPosition+len(oldStr):]

		var newContentParts []string
		if contentBeforeMatch != "" {
			newContentParts = append(newContentParts, contentBeforeMatch)
		}

		newContentParts = append(newContentParts, beforeMatch+newStr+afterMatch)

		if contentAfterMatch != "" {
			newContentParts = append(newContentParts, contentAfterMatch)
		}

		newContent = strings.Join(newContentParts, "\n")
		newStrStartLine = match.StartLine
		newStrEndLine = match.StartLine + len(strings.Split(newStr, "\n")) - 1
	}

	// Calculate the difference in line count
	oldStrLines := len(strings.Split(oldStr, "\n"))
	newStrLines := len(strings.Split(newStr, "\n"))
	numLinesDiff := newStrLines - oldStrLines

	return makeSuccessResult(newContent, newStr, newStrStartLine, newStrEndLine, numLinesDiff)
}

// HandleStrReplace handles string replacement command
func (tool *StrReplaceEditorTool) HandleStrReplace(path, fileContents string, strReplaceEntries []StrReplaceEntry) string {
	log.Printf("Handling str_replace command for %s", path)

	fullPath := filepath.Join(tool.WorkspaceRootPath, path)
	log.Printf("Convert to absolute path: %s", fullPath)

	strReplaceEntriesWithIndex := prepareStrReplaceEntries(strReplaceEntries)

	fileTextInfo := prepareTextForEditing(fileContents)
	originalContent := fileTextInfo.Content
	originalLineEnding := fileTextInfo.OriginalLineEnding

	currentContent := originalContent
	results := make(map[int]EditResult)

	for _, entry := range strReplaceEntriesWithIndex {
		overlappingEntry := findOverlappingEntry(entry, strReplaceEntriesWithIndex)

		var result EditResult
		if overlappingEntry != nil {
			result = EditResult{
				IsError:               true,
				Index:                 entry.Index,
				OldStr:                entry.OldStr,
				OldStrStartLineNumber: entry.OldStrStartLineNumber,
				OldStrEndLineNumber:   entry.OldStrEndLineNumber,
				NumLinesDiff:          0,
				GenMessageFunc: func(result *EditResult) string {
					return genOverlappingEntryErrorMessage(entry, *overlappingEntry)
				},
			}
		} else {
			result = tool.singleStrReplace(
				path,
				currentContent,
				entry.OldStr,
				entry.NewStr,
				entry.Index,
				entry.OldStrStartLineNumber,
				entry.OldStrEndLineNumber,
			)
		}

		results[entry.Index] = result

		if !result.IsError && result.NewContent != "" {
			currentContent = result.NewContent
		}
	}

	// Convert results map to slice
	var resultSlice []EditResult
	for _, result := range results {
		resultSlice = append(resultSlice, result)
	}

	toolResponse := tool.prepareToolResponse(fullPath, originalContent, currentContent, originalLineEnding, "str_replace", resultSlice)
	return toolResponse
}

// prepareToolResponse prepares the tool response message
func (tool *StrReplaceEditorTool) prepareToolResponse(path, originalContent, newContent, originalLineEnding, command string, results []EditResult) string {
	log.Printf("Preparing tool response for %s command on %s", command, path)

	// Check if content changed
	originalContentWithLineEndings := restoreLineEndings(originalContent, originalLineEnding)
	newContentWithLineEndings := restoreLineEndings(newContent, originalLineEnding)

	if originalContentWithLineEndings != newContentWithLineEndings {
		log.Printf("Content changed, writing file")

		// Write file
		err := ioutil.WriteFile(path, []byte(newContentWithLineEndings), 0644)
		if err != nil {
			return fmt.Sprintf(`{"type": "error", "message": "Error writing file %s: %v"}`, path, err)
		}

		// Wait for any auto-formatting to complete
		time.Sleep(time.Duration(tool.WaitForAutoFormatMs) * time.Millisecond)

		// Sort results by line number for correct line number adjustments
		sort.Slice(results, func(i, j int) bool {
			startI := -1
			startJ := -1

			if results[i].NewStrStartLineNumber != nil {
				startI = *results[i].NewStrStartLineNumber
			}

			if results[j].NewStrStartLineNumber != nil {
				startJ = *results[j].NewStrStartLineNumber
			}

			return startI < startJ
		})

		lineShift := 0
		for i := range results {
			result := &results[i]
			if result.NewStrStartLineNumber != nil && result.NewStrEndLineNumber != nil {
				log.Printf("Adjusting line numbers for result: start=%d, end=%d, shift=%d",
					*result.NewStrStartLineNumber, *result.NewStrEndLineNumber, lineShift)

				newStart := *result.NewStrStartLineNumber + lineShift
				newEnd := *result.NewStrEndLineNumber + lineShift
				result.NewStrStartLineNumber = &newStart
				result.NewStrEndLineNumber = &newEnd
				result.NewContent = newContent

				lineShift += result.NumLinesDiff
			}

			result.NewContent = newContent
		}
	} else {
		log.Printf("No content changes detected")
	}

	// Check if all edits succeeded or failed
	isAllErrors := true
	isAllSuccess := true

	for _, result := range results {
		if !result.IsError {
			isAllErrors = false
		}
		if result.IsError {
			isAllSuccess = false
		}
	}

	log.Printf("Results summary: all errors=%t, all success=%t, total results=%d",
		isAllErrors, isAllSuccess, len(results))

	// Generate result messages
	var resultMessages []string
	for _, result := range results {
		var message string
		if result.GenMessageFunc != nil {
			message = result.GenMessageFunc(&result)
		} else {
			message = "No message available"
		}
		resultMessages = append(resultMessages, fmt.Sprintf("Result for %s for entry with index [%d]:\n%s\n", command, result.Index, message))
	}

	resultMessagesStr := strings.Join(resultMessages, "\n")

	// Generate summary message
	var summaryMessage string
	if isAllErrors {
		summaryMessage = fmt.Sprintf(`Failed to edit the file %s. See below for details.
%s
Fix failed %s entries accordingly and try again.
`, path, resultMessagesStr, command)
	} else if isAllSuccess {
		summaryMessage = fmt.Sprintf(`Successfully edited the file %s.
%s
Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`, path, resultMessagesStr)
	} else {
		summaryMessage = fmt.Sprintf(`Partially edited the file %s. See below for details.
%s
Fix failed %s entries accordingly and try again.
`, path, resultMessagesStr, command)
	}

	// Return error or success response
	var response ToolResponse
	if isAllErrors {
		log.Printf("Returning error response for %s", path)
		response = errorToolResponse(summaryMessage)
	} else {
		log.Printf("Returning success response for %s", path)
		response = successToolResponse(summaryMessage)
	}

	responseJSON, err := json.Marshal(response)
	if err != nil {
		return fmt.Sprintf(`{"type": "error", "message": "Error marshaling response: %v"}`, err)
	}

	return string(responseJSON)
}

// StrReplaceEditorFlattenTool implements the Tool interface
type StrReplaceEditorFlattenTool struct {
	editor *StrReplaceEditorTool
}

// NewStrReplaceEditorFlattenTool creates a new flattened editor tool
func NewStrReplaceEditorFlattenTool(workspaceRoot string) *StrReplaceEditorFlattenTool {
	return &StrReplaceEditorFlattenTool{
		editor: NewStrReplaceEditorTool(workspaceRoot),
	}
}

// GetDefinition returns the tool definition
func (tool *StrReplaceEditorFlattenTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "str_replace_editor_flattened",
			Description: "Custom editing tool for editing existing files with string replacement functionality",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"path": map[string]interface{}{
						"type":        "string",
						"description": "Full path to file relative to the workspace root",
					},
					"str_replace_entries": map[string]interface{}{
						"type":        "array",
						"description": "A list of entries to replace. Each entry contains old_str, new_str, and optional line numbers",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"old_str": map[string]interface{}{
									"type":        "string",
									"description": "The string in the file to replace",
								},
								"new_str": map[string]interface{}{
									"type":        "string",
									"description": "The string to replace old_str with",
								},
								"old_str_start_line_number": map[string]interface{}{
									"type":        "integer",
									"description": "The line number of the first line of old_str (1-based)",
								},
								"old_str_end_line_number": map[string]interface{}{
									"type":        "integer",
									"description": "The line number of the last line of old_str (1-based)",
								},
							},
							"required": []string{"old_str", "new_str"},
						},
					},
				},
				"required": []string{"path", "str_replace_entries"},
			},
		},
	}
}

// Execute executes the string replacement tool
func (tool *StrReplaceEditorFlattenTool) Execute(arguments string) (string, error) {
	var args struct {
		Path              string                   `json:"path"`
		StrReplaceEntries []map[string]interface{} `json:"str_replace_entries"`
	}

	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// Validate parameters
	if args.Path == "" {
		return "", fmt.Errorf("missing required parameter 'path'")
	}

	if len(args.StrReplaceEntries) == 0 {
		return "", fmt.Errorf("missing required parameter 'str_replace_entries'")
	}

	// Validate and convert entries
	entries, err := validateStrReplaceEntries(args.StrReplaceEntries)
	if err != nil {
		return "", fmt.Errorf("invalid str_replace_entries: %v", err)
	}

	// Read file
	fullPath := filepath.Join(tool.editor.WorkspaceRootPath, args.Path)
	fileContents, err := tool.editor.ReadFile(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %v", err)
	}

	// Execute string replacement
	result := tool.editor.HandleStrReplace(args.Path, fileContents, entries)
	return result, nil
}
