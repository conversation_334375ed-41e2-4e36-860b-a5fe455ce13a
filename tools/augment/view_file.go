package augment

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/sashabaranov/go-openai"
)

// ViewRange 定义查看文件的行范围
type ViewRange struct {
	StartLine int `json:"start_line"`
	EndLine   int `json:"end_line"`
}

// ViewFileTool 查看文件工具
type ViewFileTool struct {
	workspaceRootPath string
}

// NewViewFileTool 创建新的查看文件工具
func NewViewFileTool(workspaceRootPath string) *ViewFileTool {
	return &ViewFileTool{
		workspaceRootPath: workspaceRootPath,
	}
}

// GetDefinition 获取工具定义
func (vft *ViewFileTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "view_file",
			Description: "View the contents of a file or directory. Displays the result similar to 'cat -n'. Strongly prefer to use larger ranges of at least 500 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges.",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"path": map[string]interface{}{
						"type":        "string",
						"description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'",
					},
					"range": map[string]interface{}{
						"type":        "object",
						"description": "Optional parameter when path points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. {\"start_line\": 501, \"end_line\": 1000} will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting end_line to -1 shows all lines from start_line to the end of the file.",
						"properties": map[string]interface{}{
							"start_line": map[string]interface{}{
								"type":        "integer",
								"description": "Starting line number (1-based, inclusive)",
							},
							"end_line": map[string]interface{}{
								"type":        "integer",
								"description": "Ending line number (1-based, inclusive). Use -1 to show all lines from start_line to end of file",
							},
						},
						"required": []string{"start_line", "end_line"},
					},
				},
				"required": []string{"path"},
			},
		},
	}
}

// Execute 执行查看文件操作
func (vft *ViewFileTool) Execute(arguments string) (string, error) {
	var args struct {
		Path  string     `json:"path"`
		Range *ViewRange `json:"range,omitempty"`
	}

	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("invalid arguments for view_file function: %v", err)
	}

	return vft.viewFile(args.Path, args.Range)
}

// viewFile 实际的查看文件逻辑
func (vft *ViewFileTool) viewFile(path string, viewRange *ViewRange) (string, error) {
	// 解析路径
	resolvedPath, err := vft.resolvePath(path)
	if err != nil {
		return "", fmt.Errorf("cannot resolve path: %s", path)
	}

	// 检查路径是否存在
	fileInfo, err := os.Stat(resolvedPath)
	if err != nil {
		return "", fmt.Errorf("path does not exist: %s", path)
	}

	// 如果是目录，显示目录内容
	if fileInfo.IsDir() {
		return vft.listDirectory(resolvedPath)
	}

	// 如果是文件，显示文件内容
	return vft.viewFileContent(resolvedPath, viewRange)
}

// resolvePath 解析文件路径
func (vft *ViewFileTool) resolvePath(filePath string) (string, error) {
	var absPath string

	// 如果 filePath 不是绝对路径，则将 filePath 转换为绝对路径
	if !filepath.IsAbs(filePath) {
		absPath = filepath.Join(vft.workspaceRootPath, filePath)
	} else {
		absPath = filePath
	}

	// 清理路径
	absPath = filepath.Clean(absPath)

	return absPath, nil
}

// listDirectory 列出目录内容
func (vft *ViewFileTool) listDirectory(dirPath string) (string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", fmt.Errorf("failed to read directory: %v", err)
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("Directory: %s\n", dirPath))
	result.WriteString("Contents:\n")

	for _, entry := range entries {
		entryType := "file"
		if entry.IsDir() {
			entryType = "directory"
		}
		result.WriteString(fmt.Sprintf("  %s (%s)\n", entry.Name(), entryType))
	}

	return result.String(), nil
}

// viewFileContent 查看文件内容
func (vft *ViewFileTool) viewFileContent(filePath string, viewRange *ViewRange) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 如果没有指定范围，显示整个文件
	if viewRange == nil {
		return vft.readFullFile(file, filePath)
	}

	// 根据范围显示文件
	return vft.readFileWithRange(file, filePath, viewRange)
}

// readFullFile 读取整个文件
func (vft *ViewFileTool) readFullFile(file *os.File, filePath string) (string, error) {
	var result strings.Builder
	result.WriteString(fmt.Sprintf("File: %s\n", filePath))
	result.WriteString("Content:\n")

	scanner := bufio.NewScanner(file)
	lineNumber := 1

	for scanner.Scan() {
		line := scanner.Text()
		result.WriteString(fmt.Sprintf("%4d: %s\n", lineNumber, line))
		lineNumber++
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("failed to read file: %v", err)
	}

	return result.String(), nil
}

// readFileWithRange 根据范围读取文件
func (vft *ViewFileTool) readFileWithRange(file *os.File, filePath string, viewRange *ViewRange) (string, error) {
	var result strings.Builder

	// 验证范围
	if viewRange.StartLine < 1 {
		return "", fmt.Errorf("start_line must be >= 1, got %d", viewRange.StartLine)
	}

	// 计算总行数（如果需要）
	var totalLines int
	if viewRange.EndLine == -1 {
		// 需要计算总行数
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			totalLines++
		}
		if err := scanner.Err(); err != nil {
			return "", fmt.Errorf("failed to count lines: %v", err)
		}

		// 重新定位到文件开头
		file.Seek(0, 0)
		viewRange.EndLine = totalLines
	}

	// 验证结束行
	if viewRange.EndLine < viewRange.StartLine {
		return "", fmt.Errorf("end_line (%d) must be >= start_line (%d)", viewRange.EndLine, viewRange.StartLine)
	}

	result.WriteString(fmt.Sprintf("File: %s (lines %d-%d)\n", filePath, viewRange.StartLine, viewRange.EndLine))
	result.WriteString("Content:\n")

	scanner := bufio.NewScanner(file)
	lineNumber := 1

	for scanner.Scan() {
		line := scanner.Text()

		// 如果当前行在指定范围内，添加到结果
		if lineNumber >= viewRange.StartLine && lineNumber <= viewRange.EndLine {
			result.WriteString(fmt.Sprintf("%4d: %s\n", lineNumber, line))
		}

		// 如果超过了结束行，停止读取
		if lineNumber > viewRange.EndLine {
			break
		}

		lineNumber++
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("failed to read file: %v", err)
	}

	// 检查是否找到了指定范围的行
	if lineNumber-1 < viewRange.StartLine {
		return "", fmt.Errorf("file only has %d lines, but requested range starts at line %d", lineNumber-1, viewRange.StartLine)
	}

	return result.String(), nil
}

// 为了与现有工具兼容，也可以提供一个独立的函数
func ViewFile(workspaceRoot, path string, viewRange *ViewRange) (string, error) {
	tool := NewViewFileTool(workspaceRoot)
	return tool.viewFile(path, viewRange)
}
