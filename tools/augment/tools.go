package augment

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/sashabaranov/go-openai"
	"codefuse-cli/utils"
	"codefuse-cli/tools"
)

// AugmentToolManager 增强工具管理器
type AugmentToolManager struct {
	tools map[string]tools.Tool
}

// ToolManagerConfig 工具管理器配置 (保持向后兼容)
type ToolManagerConfig struct {
	WorkspaceRoot string
	Config        *utils.Config
	GitInfo       *utils.GitInfo
}

// NewAugmentToolManager 创建新的增强工具管理器
func NewAugmentToolManager() *AugmentToolManager {
	return &AugmentToolManager{
		tools: make(map[string]tools.Tool),
	}
}

// NewToolManagerWithConfig 创建带配置的工具管理器并自动注册所有工具 (保持向后兼容)
func NewToolManagerWithConfig(config *ToolManagerConfig) tools.ToolManager {
	tm := NewAugmentToolManager()
	tm.registerAllTools(config)
	return tm
}

// registerAllTools 注册所有工具
func (tm *AugmentToolManager) registerAllTools(config *ToolManagerConfig) {
	// 注册基础工具
	tm.registerBasicTools(config)
	
	// 注册文件操作工具
	tm.registerFileTools(config)
	
	// 注册远程检索工具（条件性注册）
	tm.registerRemoteTools(config)
}

// registerBasicTools 注册基础工具
func (tm *AugmentToolManager) registerBasicTools(config *ToolManagerConfig) {
	// 注册bash工具
	tm.RegisterTool("bash", NewBashTool())
}

// registerFileTools 注册文件操作工具
func (tm *AugmentToolManager) registerFileTools(config *ToolManagerConfig) {
	// 注册文件保存工具
	tm.RegisterTool("save_file", NewSaveFileTool(config.WorkspaceRoot))
	
	// 注册文件查看工具
	tm.RegisterTool("view_file", NewViewFileTool(config.WorkspaceRoot))
	
	// 注册字符串替换编辑器工具
	tm.RegisterTool("str_replace_editor_flattened", NewStrReplaceEditorFlattenTool(config.WorkspaceRoot))
}

// registerRemoteTools 注册远程检索工具（条件性注册）
func (tm *AugmentToolManager) registerRemoteTools(config *ToolManagerConfig) {
	// 检查是否应该启用远程检索功能
	if !tm.shouldEnableRemoteRetrieval(config.GitInfo) {
		log.Printf("📁 Remote retrieval tools disabled - not a supported git repository")
		return
	}

	// 注册远程检索工具
	remoteRetrieverTool, err := NewRemoteRetrieverToolWithConfig(config.Config)
	if err != nil {
		log.Printf("Warning: Failed to initialize remote retriever tool: %v", err)
	} else {
		tm.RegisterTool("remote_retriever", remoteRetrieverTool)
		log.Printf("✅ Remote retriever tool registered")
	}

	// 注册符号检索工具
	symbolRetrieverTool := NewSymbolRetrieverToolWithConfig(config.Config)
	tm.RegisterTool("symbol_retrieval", symbolRetrieverTool)
	log.Printf("✅ Symbol retriever tool registered")
}

// shouldEnableRemoteRetrieval 检查是否应该启用远程检索功能
func (tm *AugmentToolManager) shouldEnableRemoteRetrieval(gitInfo *utils.GitInfo) bool {
	// 检查是否是code.alipay.com的仓库
	if gitInfo == nil || !strings.Contains(gitInfo.RemoteURL, "code.alipay.com") {
		log.Printf("🌐 Repository is not from code.alipay.com, remote retrieval tools disabled")
		return false
	}
	
	log.Printf("✅ Git repository from code.alipay.com detected, remote retrieval tools enabled")
	return true
}

// RegisterTool 注册工具
func (tm *AugmentToolManager) RegisterTool(name string, tool tools.Tool) {
	tm.tools[name] = tool
}

// GetTools 获取所有工具定义
func (tm *AugmentToolManager) GetTools() []openai.Tool {
	var toolList []openai.Tool
	for _, tool := range tm.tools {
		toolList = append(toolList, tool.GetDefinition())
	}
	return toolList
}

// GetToolNames 获取所有已注册的工具名称
func (tm *AugmentToolManager) GetToolNames() []string {
	var names []string
	for name := range tm.tools {
		names = append(names, name)
	}
	return names
}

// HasTool 检查是否有指定工具
func (tm *AugmentToolManager) HasTool(name string) bool {
	_, exists := tm.tools[name]
	return exists
}

// ExecuteTool 执行工具
func (tm *AugmentToolManager) ExecuteTool(name string, arguments string) (string, error) {
	tool, exists := tm.tools[name]
	if !exists {
		return "", fmt.Errorf("unknown tool: %s", name)
	}
	return tool.Execute(arguments)
}

// RemoteRetrieverTool 远程代码检索工具
type RemoteRetrieverTool struct {
	retriever *CodebaseRetriever
}

// NewRemoteRetrieverTool 创建新的远程检索工具
func NewRemoteRetrieverTool() (*RemoteRetrieverTool, error) {
	retriever, err := NewCodebaseRetriever()
	if err != nil {
		return nil, fmt.Errorf("failed to create codebase retriever: %v", err)
	}

	return &RemoteRetrieverTool{
		retriever: retriever,
	}, nil
}

// NewRemoteRetrieverToolWithConfig 创建带配置的远程检索工具
func NewRemoteRetrieverToolWithConfig(config *utils.Config) (*RemoteRetrieverTool, error) {
	retriever, err := NewCodebaseRetrieverWithConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create codebase retriever: %v", err)
	}

	return &RemoteRetrieverTool{
		retriever: retriever,
	}, nil
}

// GetDefinition 获取工具定义
func (rt *RemoteRetrieverTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "remote_retriever",
			Description: "Search and retrieve relevant code snippets from remote codebase using semantic search",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"query": map[string]interface{}{
						"type":        "string",
						"description": "Natural language description of the code you're looking for",
					},
					"repo_url": map[string]interface{}{
						"type":        "string",
						"description": "Remote repository URL (e.g., *******************:common_release/codegencore.git)",
					},
					"branch": map[string]interface{}{
						"type":        "string",
						"description": "Branch name to search in (e.g., master, main)",
					},
				},
				"required": []string{"query", "repo_url", "branch"},
			},
		},
	}
}

// RemoteRetrieverArgs 远程检索参数
type RemoteRetrieverArgs struct {
	Query   string `json:"query"`
	RepoURL string `json:"repo_url"`
	Branch  string `json:"branch"`
}

// Execute 执行远程检索
func (rt *RemoteRetrieverTool) Execute(arguments string) (string, error) {
	var args RemoteRetrieverArgs
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证参数
	if args.Query == "" {
		return "", fmt.Errorf("query is required")
	}
	if args.RepoURL == "" {
		return "", fmt.Errorf("repo_url is required")
	}
	if args.Branch == "" {
		return "", fmt.Errorf("branch is required")
	}

	// 执行检索
	ctx := context.Background()
	result, err := rt.retriever.Retrieve(ctx, args.Query, args.RepoURL, args.Branch)
	if err != nil {
		return "", fmt.Errorf("retrieval failed: %v", err)
	}

	return result, nil
}

// AugmentProvider Augment算法提供者
type AugmentProvider struct{}

// GetName 获取Provider名称
func (ap *AugmentProvider) GetName() string {
	return "augment"
}

// GetDescription 获取Provider描述
func (ap *AugmentProvider) GetDescription() string {
	return "Augment Tools Provider - Enhanced code search and retrieval with remote codebase access"
}

// CreateToolManager 创建ToolManager
func (ap *AugmentProvider) CreateToolManager(config *tools.ProviderConfig) tools.ToolManager {
	// 转换配置格式
	legacyConfig := &ToolManagerConfig{
		WorkspaceRoot: config.WorkspaceRoot,
		Config:        config.Config,
		GitInfo:       config.GitInfo,
	}
	
	return NewToolManagerWithConfig(legacyConfig)
}

// CreatePromptManager 创建PromptManager
func (ap *AugmentProvider) CreatePromptManager(config *tools.ProviderConfig) tools.PromptManager {
	return NewPromptManager(config.WorkspaceRoot, config.GitInfo)
}
