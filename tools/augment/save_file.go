package augment

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
)

// FilePath 文件路径结构
type FilePath struct {
	AbsPath string `json:"abs_path"`
	RelPath string `json:"rel_path"`
}

// SaveFileTool 保存文件工具
type SaveFileTool struct {
	workspaceRootPath string
}

// NewSaveFileTool 创建新的保存文件工具
func NewSaveFileTool(workspaceRootPath string) *SaveFileTool {
	return &SaveFileTool{
		workspaceRootPath: workspaceRootPath,
	}
}

// GetDefinition 获取工具定义
func (sft *SaveFileTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "save_file",
			Description: "Save a new file. Use this tool to write new files with the attached content. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely.",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "The path of the file to save.",
					},
					"file_content": map[string]interface{}{
						"type":        "string",
						"description": "The content of the file.",
					},
					"add_last_line_newline": map[string]interface{}{
						"type":        "boolean",
						"description": "Whether to add a newline at the end of the file (default: true).",
					},
				},
				"required": []string{"file_path", "file_content"},
			},
		},
	}
}

// Execute 执行保存文件操作
func (sft *SaveFileTool) Execute(arguments string) (string, error) {
	var args struct {
		FilePath           string `json:"file_path"`
		FileContent        string `json:"file_content"`
		AddLastLineNewline *bool  `json:"add_last_line_newline,omitempty"`
	}

	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		return "", fmt.Errorf("invalid arguments for save_file function: %v", err)
	}

	// 默认值处理
	addNewline := true
	if args.AddLastLineNewline != nil {
		addNewline = *args.AddLastLineNewline
	}

	result, err := sft.call(args.FilePath, args.FileContent, addNewline)
	if err != nil {
		return "", err
	}

	// 返回JSON格式的结果
	resultJSON, _ := json.Marshal(result)
	return string(resultJSON), nil
}

// call 实际的保存文件逻辑
func (sft *SaveFileTool) call(filePath string, fileContent string, addLastLineNewline bool) (map[string]interface{}, error) {
	// Add newline if requested
	finalContent := fileContent
	if addLastLineNewline {
		finalContent += "\n"
	}

	// Get the resolved path
	resolvedPath, err := sft.resolvePath(filePath)
	if err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("Cannot resolve path: %s", filePath),
		}, nil
	}

	// Check if file exists
	if _, err := os.Stat(resolvedPath.AbsPath); err == nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("File already exists: %s", resolvedPath.AbsPath),
		}, nil
	}

	// Create directories if they don't exist
	dir := filepath.Dir(resolvedPath.AbsPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("Failed to create directories: %v", err),
		}, nil
	}

	// Write file
	if err := os.WriteFile(resolvedPath.AbsPath, []byte(finalContent), 0644); err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("Failed to save file: %s: %v", filePath, err),
		}, nil
	}

	return map[string]interface{}{
		"success": fmt.Sprintf("File saved. Saved file %s", filePath),
	}, nil
}

// resolvePath 解析文件路径
func (sft *SaveFileTool) resolvePath(filePath string) (*FilePath, error) {
	var absPath string

	// 如果 filePath 不是绝对路径，则将 filePath 转换为绝对路径
	if !filepath.IsAbs(filePath) {
		absPath = filepath.Join(sft.workspaceRootPath, filePath)
	} else {
		absPath = filePath
	}

	// 清理路径
	absPath = filepath.Clean(absPath)

	// 计算相对路径
	relPath, err := filepath.Rel(sft.workspaceRootPath, absPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate relative path: %v", err)
	}

	return &FilePath{
		AbsPath: absPath,
		RelPath: relPath,
	}, nil
}
