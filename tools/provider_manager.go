package tools

import (
	"fmt"
	"log"
	"os"
	"strings"
)

// ProviderManager 管理所有的算法提供者
type ProviderManager struct {
	providers map[string]Provider
}

// NewProviderManager 创建新的ProviderManager
func NewProviderManager() *ProviderManager {
	return &ProviderManager{
		providers: make(map[string]Provider),
	}
}

// RegisterProvider 注册一个Provider
func (pm *ProviderManager) RegisterProvider(provider Provider) {
	pm.providers[provider.GetName()] = provider
	log.Printf("✅ Registered provider: %s - %s", provider.GetName(), provider.GetDescription())
}

// GetProvider 根据名称获取Provider
func (pm *ProviderManager) GetProvider(name string) (Provider, error) {
	provider, exists := pm.providers[name]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", name)
	}
	return provider, nil
}

// GetAvailableProviders 获取所有可用的Provider名称
func (pm *ProviderManager) GetAvailableProviders() []string {
	var names []string
	for name := range pm.providers {
		names = append(names, name)
	}
	return names
}

// GetDefaultProvider 获取默认Provider (目前是augment)
func (pm *ProviderManager) GetDefaultProvider() string {
	return "augment"
}

// GetProviderFromEnv 从环境变量获取要使用的Provider
func (pm *ProviderManager) GetProviderFromEnv() string {
	providerName := os.Getenv("CODEFUSE_PROVIDER")
	if providerName == "" {
		providerName = pm.GetDefaultProvider()
	}
	
	// 标准化provider名称
	providerName = strings.ToLower(strings.TrimSpace(providerName))
	
	log.Printf("🎯 Using provider: %s", providerName)
	return providerName
}

// CreateToolManager 创建指定Provider的ToolManager
func (pm *ProviderManager) CreateToolManager(providerName string, config *ProviderConfig) (ToolManager, error) {
	provider, err := pm.GetProvider(providerName)
	if err != nil {
		return nil, err
	}
	
	return provider.CreateToolManager(config), nil
}

// CreatePromptManager 创建指定Provider的PromptManager
func (pm *ProviderManager) CreatePromptManager(providerName string, config *ProviderConfig) (PromptManager, error) {
	provider, err := pm.GetProvider(providerName)
	if err != nil {
		return nil, err
	}
	
	return provider.CreatePromptManager(config), nil
}

// PrintAvailableProviders 打印所有可用的Provider
func (pm *ProviderManager) PrintAvailableProviders() {
	log.Printf("🏭 Available Providers:")
	for name, provider := range pm.providers {
		log.Printf("  - %s: %s", name, provider.GetDescription())
	}
} 