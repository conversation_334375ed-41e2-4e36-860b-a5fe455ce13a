package claude_code

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/sashabaranov/go-openai"
)

// FileEditTool 文件编辑工具
type FileEditTool struct {
	workspaceRoot string
	readTimestamps map[string]time.Time // 文件读取时间戳映射
}

// FileEditInput 文件编辑工具输入参数
type FileEditInput struct {
	FilePath  string `json:"file_path"`  // 要修改的绝对路径
	OldString string `json:"old_string"` // 要替换的文本
	NewString string `json:"new_string"` // 新的文本内容
}

// FileEditResult 文件编辑结果
type FileEditResult struct {
	FilePath     string `json:"file_path"`
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	LinesChanged int    `json:"lines_changed"`
	Snippet      string `json:"snippet,omitempty"`
	StartLine    int    `json:"start_line,omitempty"`
}

// NewFileEditTool 创建文件编辑工具
func NewFileEditTool(workspaceRoot string) *FileEditTool {
	return &FileEditTool{
		workspaceRoot:  workspaceRoot,
		readTimestamps: make(map[string]time.Time),
	}
}

// GetDefinition 获取工具定义
func (t *FileEditTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "file_edit",
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "The absolute path to the file to modify",
					},
					"old_string": map[string]interface{}{
						"type":        "string", 
						"description": "The text to replace (must be unique within the file, and must match the file contents exactly, including all whitespace and indentation)",
					},
					"new_string": map[string]interface{}{
						"type":        "string",
						"description": "The text to replace it with",
					},
				},
				"required": []string{"file_path", "old_string", "new_string"},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *FileEditTool) getDescription() string {
	return `This is a tool for editing files. For moving or renaming files, you should generally use the bash tool with the 'mv' command instead. For larger edits, use the write tool to overwrite files.

Before using this tool:

1. Use the view tool to understand the file's contents and context

2. Verify the directory path is correct (only applicable when creating new files):
   - Use the ls tool to verify the parent directory exists and is the correct location

To make a file edit, provide the following:
1. file_path: The absolute path to the file to modify (must be absolute, not relative)
2. old_string: The text to replace (must be unique within the file, and must match the file contents exactly, including all whitespace and indentation)
3. new_string: The edited text to replace the old_string

The tool will replace ONE occurrence of old_string with new_string in the specified file.

CRITICAL REQUIREMENTS FOR USING THIS TOOL:

1. UNIQUENESS: The old_string MUST uniquely identify the specific instance you want to change. This means:
   - Include AT LEAST 3-5 lines of context BEFORE the change point
   - Include AT LEAST 3-5 lines of context AFTER the change point
   - Include all whitespace, indentation, and surrounding code exactly as it appears in the file

2. SINGLE INSTANCE: This tool can only change ONE instance at a time. If you need to change multiple instances:
   - Make separate calls to this tool for each instance
   - Each call must uniquely identify its specific instance using extensive context

3. VERIFICATION: Before using this tool:
   - Check how many instances of the target text exist in the file
   - If multiple instances exist, gather enough context to uniquely identify each one
   - Plan separate tool calls for each instance

WARNING: If you do not follow these requirements:
   - The tool will fail if old_string matches multiple locations
   - The tool will fail if old_string doesn't match exactly (including whitespace)
   - You may change the wrong instance if you don't include enough context

When making edits:
   - Ensure the edit results in idiomatic, correct code
   - Do not leave the code in a broken state
   - Always use absolute file paths (starting with /)

If you want to create a new file, use:
   - A new file path, including dir name if needed
   - An empty old_string
   - The new file's contents as new_string

Remember: when making multiple file edits in a row to the same file, you should prefer to send all edits in a single message with multiple calls to this tool, rather than multiple messages with a single call each.`
}

// Execute 执行文件编辑
func (t *FileEditTool) Execute(arguments string) (string, error) {
	var input FileEditInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证输入
	if validationErr := t.validateInput(input); validationErr != nil {
		result := FileEditResult{
			FilePath: input.FilePath,
			Success:  false,
			Message:  validationErr.Error(),
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON), nil
	}

	// 执行编辑
	result, err := t.applyEdit(input)
	if err != nil {
		result = FileEditResult{
			FilePath: input.FilePath,
			Success:  false,
			Message:  err.Error(),
		}
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// validateInput 验证输入参数
func (t *FileEditTool) validateInput(input FileEditInput) error {
	// 检查 old_string 和 new_string 是否相同
	if input.OldString == input.NewString {
		return fmt.Errorf("no changes to make: old_string and new_string are exactly the same")
	}

	// 转换为绝对路径
	fullFilePath := input.FilePath
	if !filepath.IsAbs(fullFilePath) {
		fullFilePath = filepath.Join(t.workspaceRoot, input.FilePath)
	}

	// 检查文件是否存在以及是否要创建新文件
	fileExists := true
	if _, err := os.Stat(fullFilePath); os.IsNotExist(err) {
		fileExists = false
	}

	if fileExists && input.OldString == "" {
		return fmt.Errorf("cannot create new file - file already exists")
	}

	if !fileExists && input.OldString == "" {
		// 创建新文件的情况，验证通过
		return nil
	}

	if !fileExists {
		// 尝试查找相似文件
		similarFile := t.findSimilarFile(fullFilePath)
		message := "file does not exist"
		if similarFile != "" {
			message += fmt.Sprintf(". Did you mean %s?", similarFile)
		}
		return fmt.Errorf(message)
	}

	// 检查是否是 Jupyter Notebook
	if strings.HasSuffix(fullFilePath, ".ipynb") {
		return fmt.Errorf("file is a Jupyter Notebook. Use the notebook_edit tool to edit this file")
	}

	// 检查文件是否已读取过
	readTimestamp, hasBeenRead := t.readTimestamps[fullFilePath]
	if !hasBeenRead {
		return fmt.Errorf("file has not been read yet. Read it first before writing to it")
	}

	// 检查文件是否在读取后被修改
	fileInfo, err := os.Stat(fullFilePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %v", err)
	}

	if fileInfo.ModTime().After(readTimestamp) {
		return fmt.Errorf("file has been modified since read, either by the user or by a linter. Read it again before attempting to write it")
	}

	// 读取文件内容并检查 old_string
	content, err := os.ReadFile(fullFilePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %v", err)
	}

	fileContent := string(content)
	if !strings.Contains(fileContent, input.OldString) {
		return fmt.Errorf("string to replace not found in file")
	}

	// 检查匹配次数
	matches := strings.Count(fileContent, input.OldString)
	if matches > 1 {
		return fmt.Errorf("found %d matches of the string to replace. For safety, this tool only supports replacing exactly one occurrence at a time. Add more lines of context to your edit and try again", matches)
	}

	return nil
}

// findSimilarFile 查找相似文件
func (t *FileEditTool) findSimilarFile(filePath string) string {
	dir := filepath.Dir(filePath)
	baseName := strings.TrimSuffix(filepath.Base(filePath), filepath.Ext(filePath))
	
	entries, err := os.ReadDir(dir)
	if err != nil {
		return ""
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}
		entryBaseName := strings.TrimSuffix(entry.Name(), filepath.Ext(entry.Name()))
		if entryBaseName == baseName && entry.Name() != filepath.Base(filePath) {
			return filepath.Join(dir, entry.Name())
		}
	}

	return ""
}

// applyEdit 应用编辑
func (t *FileEditTool) applyEdit(input FileEditInput) (FileEditResult, error) {
	fullFilePath := input.FilePath
	if !filepath.IsAbs(fullFilePath) {
		fullFilePath = filepath.Join(t.workspaceRoot, input.FilePath)
	}

	var originalContent string
	var updatedContent string

	if input.OldString == "" {
		// 创建新文件
		originalContent = ""
		updatedContent = input.NewString
		
		// 确保目录存在
		dir := filepath.Dir(fullFilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return FileEditResult{}, fmt.Errorf("failed to create directory: %v", err)
		}
	} else {
		// 编辑现有文件
		content, err := os.ReadFile(fullFilePath)
		if err != nil {
			return FileEditResult{}, fmt.Errorf("failed to read file: %v", err)
		}
		
		originalContent = string(content)
		
		if input.NewString == "" {
			// 删除内容的特殊处理
			if !strings.HasSuffix(input.OldString, "\n") && 
			   strings.Contains(originalContent, input.OldString+"\n") {
				updatedContent = strings.Replace(originalContent, input.OldString+"\n", input.NewString, 1)
			} else {
				updatedContent = strings.Replace(originalContent, input.OldString, input.NewString, 1)
			}
		} else {
			updatedContent = strings.Replace(originalContent, input.OldString, input.NewString, 1)
		}
		
		if updatedContent == originalContent {
			return FileEditResult{}, fmt.Errorf("original and edited file match exactly. Failed to apply edit")
		}
	}

	// 写入文件
	if err := os.WriteFile(fullFilePath, []byte(updatedContent), 0644); err != nil {
		return FileEditResult{}, fmt.Errorf("failed to write file: %v", err)
	}

	// 更新读取时间戳
	fileInfo, err := os.Stat(fullFilePath)
	if err == nil {
		t.readTimestamps[fullFilePath] = fileInfo.ModTime()
	}

	// 生成代码片段
	snippet, startLine := t.getSnippet(originalContent, input.OldString, input.NewString)
	
	// 计算改变的行数
	linesChanged := len(strings.Split(input.NewString, "\n"))
	if input.OldString != "" {
		linesChanged = len(strings.Split(input.NewString, "\n")) - len(strings.Split(input.OldString, "\n"))
	}

	return FileEditResult{
		FilePath:     input.FilePath,
		Success:      true,
		Message:      fmt.Sprintf("The file %s has been updated successfully", input.FilePath),
		LinesChanged: linesChanged,
		Snippet:      snippet,
		StartLine:    startLine,
	}, nil
}

// getSnippet 获取编辑后的代码片段，用于显示上下文
func (t *FileEditTool) getSnippet(originalText, oldStr, newStr string) (string, int) {
	const nLinesSnippet = 4 // 上下文行数

	if oldStr == "" {
		// 新文件的情况
		lines := strings.Split(newStr, "\n")
		if len(lines) <= nLinesSnippet*2 {
			return newStr, 1
		}
		snippet := strings.Join(lines[:nLinesSnippet*2], "\n")
		return snippet, 1
	}

	// 找到替换位置
	beforeParts := strings.Split(originalText, oldStr)
	if len(beforeParts) < 2 {
		return "", 0
	}
	
	before := beforeParts[0]
	beforeLines := strings.Split(before, "\n")
	replacementLine := len(beforeLines) - 1

	// 生成新文件内容
	newFileContent := strings.Replace(originalText, oldStr, newStr, 1)
	newFileLines := strings.Split(newFileContent, "\n")

	// 计算片段的开始和结束行
	startLine := max(0, replacementLine-nLinesSnippet)
	endLine := replacementLine + nLinesSnippet + len(strings.Split(newStr, "\n"))
	if endLine >= len(newFileLines) {
		endLine = len(newFileLines) - 1
	}

	// 获取片段
	snippetLines := newFileLines[startLine : endLine+1]
	snippet := strings.Join(snippetLines, "\n")

	// 添加行号
	snippet = t.addLineNumbers(snippet, startLine+1)

	return snippet, startLine + 1
}

// addLineNumbers 添加行号
func (t *FileEditTool) addLineNumbers(content string, startLine int) string {
	lines := strings.Split(content, "\n")
	var numberedLines []string
	
	for i, line := range lines {
		lineNum := startLine + i
		numberedLines = append(numberedLines, fmt.Sprintf("%6d  %s", lineNum, line))
	}
	
	return strings.Join(numberedLines, "\n")
}

// max 返回两个整数的最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// SetReadTimestamp 设置文件读取时间戳（供其他工具调用）
func (t *FileEditTool) SetReadTimestamp(filePath string, timestamp time.Time) {
	t.readTimestamps[filePath] = timestamp
}

// GetReadTimestamp 获取文件读取时间戳
func (t *FileEditTool) GetReadTimestamp(filePath string) (time.Time, bool) {
	timestamp, exists := t.readTimestamps[filePath]
	return timestamp, exists
}
