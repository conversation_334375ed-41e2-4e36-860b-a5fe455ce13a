package claude_code

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/sashabaranov/go-openai"
)

// MemoryReadTool 内存读取工具
type MemoryReadTool struct {
	workspaceRoot string
	memoryDir     string
}

// MemoryReadInput 内存读取工具输入参数
type MemoryReadInput struct {
	FilePath *string `json:"file_path,omitempty"` // 可选的内存文件路径
}

// MemoryReadResult 内存读取结果
type MemoryReadResult struct {
	Content string `json:"content"`
}

// NewMemoryReadTool 创建内存读取工具
func NewMemoryReadTool(workspaceRoot string) *MemoryReadTool {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}
	
	// 创建内存目录路径 ~/.claude/memory
	memoryDir := filepath.Join(homeDir, ".claude", "memory")
	
	return &MemoryReadTool{
		workspaceRoot: workspaceRoot,
		memoryDir:     memoryDir,
	}
}

// GetDefinition 获取工具定义
func (t *MemoryReadTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "memory_read",
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "Optional path to a specific memory file to read",
					},
				},
				"required": []string{},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *MemoryReadTool) getDescription() string {
	return "Read memory files or list memory directory contents. If file_path is specified, reads that specific memory file. Otherwise, returns the index file and list of all memory files."
}

// Execute 执行内存读取
func (t *MemoryReadTool) Execute(arguments string) (string, error) {
	var input MemoryReadInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证输入
	if validationErr := t.validateInput(input); validationErr != nil {
		return "", validationErr
	}

	// 执行读取
	result, err := t.readMemory(input)
	if err != nil {
		return "", err
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// validateInput 验证输入参数
func (t *MemoryReadTool) validateInput(input MemoryReadInput) error {
	if input.FilePath != nil {
		fullPath := filepath.Join(t.memoryDir, *input.FilePath)
		
		// 检查路径是否在内存目录内（安全检查）
		if !strings.HasPrefix(fullPath, t.memoryDir) {
			return fmt.Errorf("invalid memory file path")
		}
		
		// 检查文件是否存在
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			return fmt.Errorf("memory file does not exist")
		}
	}
	
	return nil
}

// readMemory 读取内存内容
func (t *MemoryReadTool) readMemory(input MemoryReadInput) (MemoryReadResult, error) {
	// 确保内存目录存在
	if err := os.MkdirAll(t.memoryDir, 0755); err != nil {
		return MemoryReadResult{}, fmt.Errorf("failed to create memory directory: %v", err)
	}

	// 如果指定了文件路径，读取该文件
	if input.FilePath != nil {
		return t.readSpecificFile(*input.FilePath)
	}

	// 否则返回索引和文件列表
	return t.readIndexAndFileList()
}

// readSpecificFile 读取指定的内存文件
func (t *MemoryReadTool) readSpecificFile(filePath string) (MemoryReadResult, error) {
	fullPath := filepath.Join(t.memoryDir, filePath)
	
	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return MemoryReadResult{}, fmt.Errorf("memory file does not exist")
	}
	
	// 读取文件内容
	content, err := os.ReadFile(fullPath)
	if err != nil {
		return MemoryReadResult{}, fmt.Errorf("failed to read memory file: %v", err)
	}
	
	return MemoryReadResult{
		Content: string(content),
	}, nil
}

// readIndexAndFileList 读取索引文件和文件列表
func (t *MemoryReadTool) readIndexAndFileList() (MemoryReadResult, error) {
	// 读取索引文件
	indexPath := filepath.Join(t.memoryDir, "index.md")
	var indexContent string
	
	if content, err := os.ReadFile(indexPath); err == nil {
		indexContent = string(content)
	}
	
	// 获取文件列表
	var fileList []string
	err := filepath.WalkDir(t.memoryDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // 忽略错误，继续遍历
		}
		
		if !d.IsDir() {
			// 获取相对于内存目录的路径
			relPath, err := filepath.Rel(t.memoryDir, path)
			if err == nil {
				fileList = append(fileList, fmt.Sprintf("- %s", filepath.Join(t.memoryDir, relPath)))
			}
		}
		
		return nil
	})
	
	if err != nil {
		return MemoryReadResult{}, fmt.Errorf("failed to list memory files: %v", err)
	}
	
	// 构建完整的内容
	quotes := "'''"
	content := fmt.Sprintf(`Here are the contents of the root memory file, %s:
%s
%s
%s

Files in the memory directory:
%s`, indexPath, quotes, indexContent, quotes, strings.Join(fileList, "\n"))
	
	return MemoryReadResult{
		Content: content,
	}, nil
}
