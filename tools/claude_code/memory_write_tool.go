package claude_code

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/sasha<PERSON>nov/go-openai"
)

// MemoryWriteTool 内存写入工具
type MemoryWriteTool struct {
	workspaceRoot string
	memoryDir     string
}

// MemoryWriteInput 内存写入工具输入参数
type MemoryWriteInput struct {
	FilePath string `json:"file_path"` // 内存文件路径
	Content  string `json:"content"`   // 要写入的内容
}

// MemoryWriteResult 内存写入结果
type MemoryWriteResult struct {
	Message string `json:"message"`
}

// NewMemoryWriteTool 创建内存写入工具
func NewMemoryWriteTool(workspaceRoot string) *MemoryWriteTool {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}
	
	// 创建内存目录路径 ~/.claude/memory
	memoryDir := filepath.Join(homeDir, ".claude", "memory")
	
	return &MemoryWriteTool{
		workspaceRoot: workspaceRoot,
		memoryDir:     memoryDir,
	}
}

// GetDefinition 获取工具定义
func (t *MemoryWriteTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "memory_write",
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "Path to the memory file to write",
					},
					"content": map[string]interface{}{
						"type":        "string",
						"description": "Content to write to the file",
					},
				},
				"required": []string{"file_path", "content"},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *MemoryWriteTool) getDescription() string {
	return "Write content to a memory file. Creates directories recursively if needed."
}

// Execute 执行内存写入
func (t *MemoryWriteTool) Execute(arguments string) (string, error) {
	var input MemoryWriteInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证输入
	if validationErr := t.validateInput(input); validationErr != nil {
		return "", validationErr
	}

	// 执行写入
	result, err := t.writeMemory(input)
	if err != nil {
		return "", err
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// validateInput 验证输入参数
func (t *MemoryWriteTool) validateInput(input MemoryWriteInput) error {
	// 检查file_path是否为空
	if input.FilePath == "" {
		return fmt.Errorf("file_path is required")
	}
	
	// 构建完整路径
	fullPath := filepath.Join(t.memoryDir, input.FilePath)
	
	// 检查路径是否在内存目录内（安全检查）
	if !strings.HasPrefix(fullPath, t.memoryDir) {
		return fmt.Errorf("invalid memory file path")
	}
	
	return nil
}

// writeMemory 写入内存内容
func (t *MemoryWriteTool) writeMemory(input MemoryWriteInput) (MemoryWriteResult, error) {
	// 构建完整路径
	fullPath := filepath.Join(t.memoryDir, input.FilePath)
	
	// 获取目录路径
	dirPath := filepath.Dir(fullPath)
	
	// 递归创建目录
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return MemoryWriteResult{}, fmt.Errorf("failed to create directory: %v", err)
	}
	
	// 写入文件
	if err := os.WriteFile(fullPath, []byte(input.Content), 0644); err != nil {
		return MemoryWriteResult{}, fmt.Errorf("failed to write file: %v", err)
	}
	
	return MemoryWriteResult{
		Message: "Saved",
	}, nil
}

