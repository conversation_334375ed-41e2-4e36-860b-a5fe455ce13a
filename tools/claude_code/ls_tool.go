package claude_code

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/sasha<PERSON>nov/go-openai"
)

const (
	MAX_FILES       = 1000
	TRUNCATED_MESSAGE = "There are more than 1000 files in the repository. Use the LS tool (passing a specific path), Bash tool, and other tools to explore nested directories. The first 1000 files and directories are included below:\n\n"
)

// LsTool ls工具结构体
type LsTool struct {
	workspaceRoot string
}

// NewLsTool 创建新的ls工具
func NewLsTool(workspaceRoot string) *LsTool {
	return &LsTool{
		workspaceRoot: workspaceRoot,
	}
}

// LsInput ls工具输入参数
type LsInput struct {
	Path string `json:"path" description:"The absolute path to the directory to list (must be absolute, not relative)"`
}

// TreeNode 树形节点结构体
type TreeNode struct {
	Name     string      `json:"name"`
	Path     string      `json:"path"`
	Type     string      `json:"type"` // "file" or "directory"
	Children []*TreeNode `json:"children,omitempty"`
}

// GetDefinition 获取工具定义
func (t *LsTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "ls",
			Description: "Lists files and directories in a given path. The path parameter must be an absolute path, not a relative path. You should generally prefer the Glob and Grep tools, if you know which directories to search.",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"path": map[string]interface{}{
						"type":        "string",
						"description": "The absolute path to the directory to list (must be absolute, not relative)",
					},
				},
				"required": []string{"path"},
			},
		},
	}
}

// Execute 执行ls工具
func (t *LsTool) Execute(argumentsStr string) (string, error) {
	var input LsInput
	if err := json.Unmarshal([]byte(argumentsStr), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证路径是否为绝对路径
	if !filepath.IsAbs(input.Path) {
		return "", fmt.Errorf("path must be absolute, not relative: %s", input.Path)
	}

	// 检查路径是否存在
	if _, err := os.Stat(input.Path); os.IsNotExist(err) {
		return "", fmt.Errorf("path does not exist: %s", input.Path)
	}

	// 列出目录内容
	result, err := t.listDirectory(input.Path, t.workspaceRoot)
	if err != nil {
		return "", fmt.Errorf("failed to list directory: %v", err)
	}

	// 排序结果
	sort.Strings(result)

	// 构建文件树
	tree := t.createFileTree(result)

	// 打印树形结构
	userTree := t.printTree(tree, 0, "", t.workspaceRoot)

	// 添加安全警告（仅供AI使用）
	safetyWarning := "\nNOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work."
	assistantTree := userTree + safetyWarning

	// 检查是否需要截断
	if len(result) >= MAX_FILES {
		return TRUNCATED_MESSAGE + assistantTree, nil
	}

	return assistantTree, nil
}

// listDirectory 递归列出目录内容
func (t *LsTool) listDirectory(initialPath string, cwd string) ([]string, error) {
	var results []string
	queue := []string{initialPath}

	for len(queue) > 0 && len(results) < MAX_FILES {
		currentPath := queue[0]
		queue = queue[1:]

		if t.skip(currentPath) {
			continue
		}

		// 如果不是初始路径，添加到结果中
		if currentPath != initialPath {
			relPath, err := filepath.Rel(cwd, currentPath)
			if err != nil {
				relPath = currentPath
			}
			if strings.HasSuffix(currentPath, string(filepath.Separator)) {
				results = append(results, relPath+string(filepath.Separator))
			} else {
				results = append(results, relPath)
			}
		}

		// 读取目录内容
		entries, err := os.ReadDir(currentPath)
		if err != nil {
			// 忽略权限错误等，继续处理
			continue
		}

		for _, entry := range entries {
			fullPath := filepath.Join(currentPath, entry.Name())
			
			if entry.IsDir() {
				queue = append(queue, fullPath+string(filepath.Separator))
			} else {
				if t.skip(fullPath) {
					continue
				}
				relPath, err := filepath.Rel(cwd, fullPath)
				if err != nil {
					relPath = fullPath
				}
				results = append(results, relPath)
				
				if len(results) >= MAX_FILES {
					break
				}
			}
		}
	}

	return results, nil
}

// createFileTree 创建文件树结构
func (t *LsTool) createFileTree(sortedPaths []string) []*TreeNode {
	root := []*TreeNode{}

	for _, path := range sortedPaths {
		parts := strings.Split(path, string(filepath.Separator))
		currentLevel := &root
		currentPath := ""

		for i, part := range parts {
			if part == "" {
				// 目录有尾随斜杠
				continue
			}

			if currentPath == "" {
				currentPath = part
			} else {
				currentPath = filepath.Join(currentPath, part)
			}
			
			isLastPart := i == len(parts)-1

			// 查找现有节点
			var existingNode *TreeNode
			for _, node := range *currentLevel {
				if node.Name == part {
					existingNode = node
					break
				}
			}

			if existingNode != nil {
				if existingNode.Children != nil {
					currentLevel = &existingNode.Children
				}
			} else {
				nodeType := "file"
				if !isLastPart {
					nodeType = "directory"
				}

				newNode := &TreeNode{
					Name: part,
					Path: currentPath,
					Type: nodeType,
				}

				if !isLastPart {
					newNode.Children = []*TreeNode{}
				}

				*currentLevel = append(*currentLevel, newNode)
				if newNode.Children != nil {
					currentLevel = &newNode.Children
				}
			}
		}
	}

	return root
}

// printTree 打印树形结构
func (t *LsTool) printTree(tree []*TreeNode, level int, prefix string, rootPath string) string {
	var result strings.Builder

	// 在根级别添加绝对路径
	if level == 0 {
		result.WriteString(fmt.Sprintf("- %s%s\n", rootPath, string(filepath.Separator)))
		prefix = "  "
	}

	for _, node := range tree {
		// 添加当前节点到结果
		suffix := ""
		if node.Type == "directory" {
			suffix = string(filepath.Separator)
		}
		result.WriteString(fmt.Sprintf("%s- %s%s\n", prefix, node.Name, suffix))

		// 递归打印子节点
		if node.Children != nil && len(node.Children) > 0 {
			result.WriteString(t.printTree(node.Children, level+1, prefix+"  ", rootPath))
		}
	}

	return result.String()
}

// skip 判断是否跳过文件或目录
func (t *LsTool) skip(path string) bool {
	baseName := filepath.Base(path)
	
	// 跳过隐藏文件和目录（以.开头）
	if path != "." && strings.HasPrefix(baseName, ".") {
		return true
	}
	
	// 跳过 __pycache__ 目录
	if strings.Contains(path, "__pycache__"+string(filepath.Separator)) {
		return true
	}
	
	return false
}
