package claude_code

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"time"
	_ "embed"

	"github.com/sashabaranov/go-openai"
)

// 嵌入ripgrep二进制文件
//go:embed ripgrep_bins/x64_darwin/rg
var ripgrepDarwinAmd64 []byte

//go:embed ripgrep_bins/arm64_darwin/rg
var ripgrepDarwinArm64 []byte

//go:embed ripgrep_bins/x64_linux/rg
var ripgrepLinuxAmd64 []byte

//go:embed ripgrep_bins/arm64_linux/rg
var ripgrepLinuxArm64 []byte

//go:embed ripgrep_bins/x64_win32/rg.exe
var ripgrepWindows []byte

const (
	MaxResults = 100
	DefaultTimeout = 10 * time.Second
)

// GrepToolInput 定义grep工具的输入参数
type GrepToolInput struct {
	Pattern string  `json:"pattern"`
	Path    *string `json:"path,omitempty"`
	Include *string `json:"include,omitempty"`
}

// GrepToolOutput 定义grep工具的输出
type GrepToolOutput struct {
	DurationMs int      `json:"duration_ms"`
	NumFiles   int      `json:"num_files"`
	Filenames  []string `json:"filenames"`
}

// FileInfo 用于排序的文件信息
type FileInfo struct {
	Path    string
	ModTime time.Time
}

// GrepTool grep工具实现
type GrepTool struct {
	workspaceRoot string
	ripgrepPath   string
}

// NewGrepTool 创建新的grep工具
func NewGrepTool(workspaceRoot string) *GrepTool {
	return &GrepTool{
		workspaceRoot: workspaceRoot,
	}
}

// getRipgrepPath 提取并返回ripgrep二进制文件路径
func (gt *GrepTool) getRipgrepPath() (string, error) {
	if gt.ripgrepPath != "" {
		return gt.ripgrepPath, nil
	}

	var data []byte
	var filename string

	switch runtime.GOOS {
	case "darwin":
		if runtime.GOARCH == "arm64" {
			data = ripgrepDarwinArm64
		} else {
			data = ripgrepDarwinAmd64
		}
		filename = "rg"
	case "linux":
		if runtime.GOARCH == "arm64" {
			data = ripgrepLinuxArm64
		} else {
			data = ripgrepLinuxAmd64
		}
		filename = "rg"
	case "windows":
		data = ripgrepWindows
		filename = "rg.exe"
	default:
		return "", fmt.Errorf("unsupported platform: %s/%s", runtime.GOOS, runtime.GOARCH)
	}

	// 创建临时文件
	tmpDir := os.TempDir()
	rgPath := filepath.Join(tmpDir, fmt.Sprintf("codefuse-cli-rg-%d-%s", os.Getpid(), filename))

	// 检查文件是否已存在且可执行
	if _, err := os.Stat(rgPath); err == nil {
		gt.ripgrepPath = rgPath
		return rgPath, nil
	}

	// 写入嵌入的二进制数据
	err := os.WriteFile(rgPath, data, 0755)
	if err != nil {
		return "", fmt.Errorf("failed to extract ripgrep: %v", err)
	}

	gt.ripgrepPath = rgPath
	return rgPath, nil
}

// GetDefinition 获取工具定义
func (gt *GrepTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "grep_search",
			Description: gt.getPrompt(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"pattern": map[string]interface{}{
						"type":        "string",
						"description": "The regular expression pattern to search for in file contents",
					},
					"path": map[string]interface{}{
						"type":        "string",
						"description": "The directory to search in. Defaults to the current working directory.",
					},
					"include": map[string]interface{}{
						"type":        "string",
						"description": "File pattern to include in the search (e.g. \"*.js\", \"*.{ts,tsx}\")",
					},
				},
				"required": []string{"pattern"},
			},
		},
	}
}

// Execute 执行grep搜索
func (gt *GrepTool) Execute(arguments string) (string, error) {
	var input GrepToolInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("invalid arguments for grep_search function: %v", err)
	}

	// 验证输入
	if err := gt.validateInput(input); err != nil {
		return "", err
	}

	// 执行搜索
	output, err := gt.search(input)
	if err != nil {
		return "", fmt.Errorf("failed to execute grep search: %v", err)
	}

	// 格式化返回结果
	return gt.formatResult(output), nil
}

// validateInput 验证输入参数
func (gt *GrepTool) validateInput(input GrepToolInput) error {
	if strings.TrimSpace(input.Pattern) == "" {
		return fmt.Errorf("pattern cannot be empty")
	}
	return nil
}

// search 执行实际的搜索
func (gt *GrepTool) search(input GrepToolInput) (*GrepToolOutput, error) {
	start := time.Now()

	// 获取ripgrep二进制路径
	rgPath, err := gt.getRipgrepPath()
	if err != nil {
		return nil, err
	}

	// 设置搜索路径
	searchPath := gt.workspaceRoot
	if input.Path != nil && *input.Path != "" {
		if filepath.IsAbs(*input.Path) {
			searchPath = *input.Path
		} else {
			searchPath = filepath.Join(gt.workspaceRoot, *input.Path)
		}
	}

	// 构建命令参数
	args := []string{"-li", input.Pattern}
	if input.Include != nil && *input.Include != "" {
		args = append(args, "--glob", *input.Include)
	}
	args = append(args, searchPath)

	// 执行ripgrep命令
	ctx, cancel := context.WithTimeout(context.Background(), DefaultTimeout)
	defer cancel()

	cmd := exec.CommandContext(ctx, rgPath, args...)
	cmd.Dir = gt.workspaceRoot

	output, err := cmd.Output()
	if err != nil {
		// 退出码1表示没有找到匹配项，这是正常情况
		if exitError, ok := err.(*exec.ExitError); ok && exitError.ExitCode() == 1 {
			return &GrepToolOutput{
				DurationMs: int(time.Since(start).Milliseconds()),
				NumFiles:   0,
				Filenames:  []string{},
			}, nil
		}
		return nil, fmt.Errorf("ripgrep command failed: %v", err)
	}

	// 解析输出
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	var filenames []string
	for _, line := range lines {
		if line != "" {
			filenames = append(filenames, line)
		}
	}

	// 按修改时间排序
	sortedFiles, err := gt.sortByModTime(filenames)
	if err != nil {
		// 如果排序失败，使用原始顺序
		sortedFiles = filenames
	}

	// 限制结果数量
	if len(sortedFiles) > MaxResults {
		sortedFiles = sortedFiles[:MaxResults]
	}

	return &GrepToolOutput{
		DurationMs: int(time.Since(start).Milliseconds()),
		NumFiles:   len(sortedFiles),
		Filenames:  sortedFiles,
	}, nil
}

// sortByModTime 按修改时间排序文件
func (gt *GrepTool) sortByModTime(filenames []string) ([]string, error) {
	var fileInfos []FileInfo

	for _, filename := range filenames {
		stat, err := os.Stat(filename)
		if err != nil {
			// 如果无法获取文件状态，跳过该文件
			continue
		}
		fileInfos = append(fileInfos, FileInfo{
			Path:    filename,
			ModTime: stat.ModTime(),
		})
	}

	// 按修改时间降序排序（最新的在前面）
	sort.Slice(fileInfos, func(i, j int) bool {
		if fileInfos[i].ModTime.Equal(fileInfos[j].ModTime) {
			// 修改时间相同时按文件名排序
			return fileInfos[i].Path < fileInfos[j].Path
		}
		return fileInfos[i].ModTime.After(fileInfos[j].ModTime)
	})

	var sortedPaths []string
	for _, info := range fileInfos {
		sortedPaths = append(sortedPaths, info.Path)
	}

	return sortedPaths, nil
}

// formatResult 格式化搜索结果
func (gt *GrepTool) formatResult(output *GrepToolOutput) string {
	if output.NumFiles == 0 {
		return "No files found"
	}

	result := fmt.Sprintf("Found %d file", output.NumFiles)
	if output.NumFiles != 1 {
		result += "s"
	}
	result += "\n" + strings.Join(output.Filenames, "\n")

	if len(output.Filenames) == MaxResults && output.NumFiles > MaxResults {
		result += "\n(Results are truncated. Consider using a more specific path or pattern.)"
	}

	return result
}

// getPrompt 获取工具描述
func (gt *GrepTool) getPrompt() string {
	return `Fast content search tool that works with any codebase size
- Searches file contents using regular expressions
- Supports full regex syntax (eg. "log.*Error", "function\\s+\\w+", etc.)
- Filter files by pattern with the include parameter (eg. "*.js", "*.{ts,tsx}")
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files containing specific patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead`
}
