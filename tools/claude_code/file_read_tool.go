package claude_code

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
)

// FileReadTool 文件读取工具
type FileReadTool struct {
	workspaceRoot      string
	readTimestamps     map[string]time.Time // 文件读取时间戳映射
	fileEditTool       *FileEditTool        // 引用FileEditTool来共享时间戳
}

// FileReadInput 文件读取工具输入参数
type FileReadInput struct {
	FilePath string `json:"file_path"`        // 要读取的绝对路径
	Offset   *int   `json:"offset,omitempty"` // 开始读取的行号（可选）
	Limit    *int   `json:"limit,omitempty"`  // 读取的行数（可选）
}

// FileReadResult 文件读取结果
type FileReadResult struct {
	Type string      `json:"type"` // "text" 或 "image"
	File interface{} `json:"file"`
}

// TextFileResult 文本文件结果
type TextFileResult struct {
	FilePath   string `json:"file_path"`
	Content    string `json:"content"`
	NumLines   int    `json:"num_lines"`
	StartLine  int    `json:"start_line"`
	TotalLines int    `json:"total_lines"`
}

// ImageFileResult 图片文件结果
type ImageFileResult struct {
	Base64    string `json:"base64"`
	MediaType string `json:"media_type"`
}

const (
	MaxOutputSize    = 0.25 * 1024 * 1024 // 0.25MB
	MaxImageSize     = 3.75 * 1024 * 1024 // 3.75MB
	MaxWidth         = 2000
	MaxHeight        = 2000
	MaxLinesToRead   = 2000
	MaxLineLength    = 2000
)

var imageExtensions = map[string]string{
	".png":  "image/png",
	".jpg":  "image/jpeg", 
	".jpeg": "image/jpeg",
	".gif":  "image/gif",
	".bmp":  "image/bmp",
	".webp": "image/webp",
}

// NewFileReadTool 创建文件读取工具
func NewFileReadTool(workspaceRoot string) *FileReadTool {
	return &FileReadTool{
		workspaceRoot:  workspaceRoot,
		readTimestamps: make(map[string]time.Time),
	}
}

// SetFileEditTool 设置FileEditTool引用以共享时间戳
func (t *FileReadTool) SetFileEditTool(fileEditTool *FileEditTool) {
	t.fileEditTool = fileEditTool
}

// GetDefinition 获取工具定义
func (t *FileReadTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "file_read",
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "The absolute path to the file to read",
					},
					"offset": map[string]interface{}{
						"type":        "integer",
						"description": "The line number to start reading from. Only provide if the file is too large to read at once",
					},
					"limit": map[string]interface{}{
						"type":        "integer", 
						"description": "The number of lines to read. Only provide if the file is too large to read at once.",
					},
				},
				"required": []string{"file_path"},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *FileReadTool) getDescription() string {
	return fmt.Sprintf(`Reads a file from the local filesystem. The file_path parameter must be an absolute path, not a relative path. By default, it reads up to %d lines starting from the beginning of the file. You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters. Any lines longer than %d characters will be truncated. For image files, the tool will display the image for you. For Jupyter notebooks (.ipynb files), use the notebook_read tool instead.`, MaxLinesToRead, MaxLineLength)
}

// Execute 执行文件读取
func (t *FileReadTool) Execute(arguments string) (string, error) {
	var input FileReadInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证输入
	if validationErr := t.validateInput(input); validationErr != nil {
		return "", validationErr
	}

	// 执行读取
	result, err := t.readFile(input)
	if err != nil {
		return "", err
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// validateInput 验证输入参数
func (t *FileReadTool) validateInput(input FileReadInput) error {
	// 转换为绝对路径
	fullFilePath := input.FilePath
	if !filepath.IsAbs(fullFilePath) {
		fullFilePath = filepath.Join(t.workspaceRoot, input.FilePath)
	}

	// 检查文件是否存在
	if _, err := os.Stat(fullFilePath); os.IsNotExist(err) {
		// 尝试查找相似文件
		similarFile := t.findSimilarFile(fullFilePath)
		message := "File does not exist."
		if similarFile != "" {
			message += fmt.Sprintf(" Did you mean %s?", similarFile)
		}
		return fmt.Errorf(message)
	}

	// 获取文件信息
	fileInfo, err := os.Stat(fullFilePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %v", err)
	}

	// 检查是否是Jupyter Notebook
	if strings.HasSuffix(fullFilePath, ".ipynb") {
		return fmt.Errorf("file is a Jupyter Notebook. Use the notebook_read tool to read this file")
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(fullFilePath))
	
	// 跳过图片文件的大小检查 - 它们有自己的大小限制
	if _, isImage := imageExtensions[ext]; !isImage {
		// 如果文件过大且没有提供 offset/limit
		if fileInfo.Size() > MaxOutputSize && input.Offset == nil && input.Limit == nil {
			return fmt.Errorf(t.formatFileSizeError(fileInfo.Size()))
		}
	}

	return nil
}

// findSimilarFile 查找相似文件
func (t *FileReadTool) findSimilarFile(filePath string) string {
	dir := filepath.Dir(filePath)
	baseName := strings.TrimSuffix(filepath.Base(filePath), filepath.Ext(filePath))
	
	entries, err := os.ReadDir(dir)
	if err != nil {
		return ""
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}
		entryBaseName := strings.TrimSuffix(entry.Name(), filepath.Ext(entry.Name()))
		if entryBaseName == baseName && entry.Name() != filepath.Base(filePath) {
			return filepath.Join(dir, entry.Name())
		}
	}

	return ""
}

// formatFileSizeError 格式化文件大小错误消息
func (t *FileReadTool) formatFileSizeError(sizeInBytes int64) string {
	return fmt.Sprintf("File content (%dKB) exceeds maximum allowed size (%dKB). Please use offset and limit parameters to read specific portions of the file, or use the grep_search tool to search for specific content.", 
		sizeInBytes/1024, MaxOutputSize/1024)
}

// readFile 读取文件
func (t *FileReadTool) readFile(input FileReadInput) (FileReadResult, error) {
	fullFilePath := input.FilePath
	if !filepath.IsAbs(fullFilePath) {
		fullFilePath = filepath.Join(t.workspaceRoot, input.FilePath)
	}

	// 更新读取时间戳
	now := time.Now()
	t.readTimestamps[fullFilePath] = now
	
	// 如果有FileEditTool引用，也更新其时间戳
	if t.fileEditTool != nil {
		t.fileEditTool.SetReadTimestamp(fullFilePath, now)
	}

	// 检查是否是图片文件
	ext := strings.ToLower(filepath.Ext(fullFilePath))
	if mediaType, isImage := imageExtensions[ext]; isImage {
		return t.readImageFile(fullFilePath, mediaType)
	}

	// 读取文本文件
	return t.readTextFile(input, fullFilePath)
}

// readImageFile 读取图片文件
func (t *FileReadTool) readImageFile(filePath, mediaType string) (FileReadResult, error) {
	// 读取文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return FileReadResult{}, fmt.Errorf("failed to read image file: %v", err)
	}

	// 检查文件大小
	if len(data) > MaxImageSize {
		// 尝试压缩图片
		compressedData, err := t.compressImage(data, filepath.Ext(filePath))
		if err != nil {
			return FileReadResult{}, fmt.Errorf("image too large and compression failed: %v", err)
		}
		data = compressedData
		mediaType = "image/jpeg" // 压缩后统一为JPEG格式
	}

	// 编码为base64
	base64Data := base64.StdEncoding.EncodeToString(data)

	result := FileReadResult{
		Type: "image",
		File: ImageFileResult{
			Base64:    base64Data,
			MediaType: mediaType,
		},
	}

	return result, nil
}

// compressImage 压缩图片
func (t *FileReadTool) compressImage(data []byte, ext string) ([]byte, error) {
	// 解码图片
	var img image.Image
	var err error
	
	reader := bytes.NewReader(data)
	switch strings.ToLower(ext) {
	case ".png":
		img, err = png.Decode(reader)
	case ".jpg", ".jpeg":
		img, err = jpeg.Decode(reader)
	default:
		return nil, fmt.Errorf("unsupported image format for compression: %s", ext)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %v", err)
	}

	// 检查是否需要调整大小
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()
	
	// 如果尺寸过大，需要调整
	if width > MaxWidth || height > MaxHeight {
		// 这里简化处理，实际应该使用图像处理库进行缩放
		// 为了保持代码简洁，我们直接以80%质量重新编码JPEG
	}

	// 重新编码为JPEG，降低质量
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: 80})
	if err != nil {
		return nil, fmt.Errorf("failed to encode compressed image: %v", err)
	}

	return buf.Bytes(), nil
}

// readTextFile 读取文本文件
func (t *FileReadTool) readTextFile(input FileReadInput, fullFilePath string) (FileReadResult, error) {
	file, err := os.Open(fullFilePath)
	if err != nil {
		return FileReadResult{}, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 设置默认值
	offset := 1
	if input.Offset != nil {
		offset = *input.Offset
	}
	// offset为0时不减1，其他情况减1转换为0-based索引
	lineOffset := offset
	if offset > 0 {
		lineOffset = offset - 1
	}

	var limit int
	if input.Limit != nil {
		limit = *input.Limit
	} else {
		limit = MaxLinesToRead
	}

	// 读取文件内容
	content, lineCount, totalLines, err := t.readTextContent(file, lineOffset, limit)
	if err != nil {
		return FileReadResult{}, err
	}

	// 检查内容大小
	if len(content) > MaxOutputSize {
		return FileReadResult{}, fmt.Errorf(t.formatFileSizeError(int64(len(content))))
	}

	// 添加行号
	contentWithLineNumbers := t.addLineNumbers(content, offset)

	result := FileReadResult{
		Type: "text",
		File: TextFileResult{
			FilePath:   input.FilePath,
			Content:    contentWithLineNumbers,
			NumLines:   lineCount,
			StartLine:  offset,
			TotalLines: totalLines,
		},
	}

	return result, nil
}

// readTextContent 读取文本内容
func (t *FileReadTool) readTextContent(file *os.File, lineOffset, limit int) (string, int, int, error) {
	scanner := bufio.NewScanner(file)
	var lines []string
	currentLine := 0
	totalLines := 0

	// 扫描所有行以获取总行数，同时收集需要的行
	for scanner.Scan() {
		line := scanner.Text()
		
		// 截断过长的行
		if len(line) > MaxLineLength {
			line = line[:MaxLineLength] + "..."
		}
		
		// 如果在目标范围内，收集该行
		if currentLine >= lineOffset && (limit == 0 || len(lines) < limit) {
			lines = append(lines, line)
		}
		
		currentLine++
		totalLines++
		
		// 如果已收集足够的行，可以停止（但还需要计算总行数）
		if limit > 0 && len(lines) >= limit && lineOffset == 0 {
			// 如果从开始读取且已达到限制，继续扫描只为计算总行数
			for scanner.Scan() {
				totalLines++
			}
			break
		}
	}

	if err := scanner.Err(); err != nil {
		return "", 0, 0, fmt.Errorf("failed to read file: %v", err)
	}

	content := strings.Join(lines, "\n")
	return content, len(lines), totalLines, nil
}

// addLineNumbers 添加行号
func (t *FileReadTool) addLineNumbers(content string, startLine int) string {
	lines := strings.Split(content, "\n")
	var numberedLines []string
	
	for i, line := range lines {
		lineNum := startLine + i
		numberedLines = append(numberedLines, fmt.Sprintf("%6d  %s", lineNum, line))
	}
	
	return strings.Join(numberedLines, "\n")
}

// GetReadTimestamp 获取文件读取时间戳
func (t *FileReadTool) GetReadTimestamp(filePath string) (time.Time, bool) {
	timestamp, exists := t.readTimestamps[filePath]
	return timestamp, exists
}
