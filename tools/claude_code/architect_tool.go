package claude_code

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
)

// ArchitectToolInput 定义Architect工具的输入参数
type ArchitectToolInput struct {
	Prompt  string `json:"prompt"`
	Context string `json:"context,omitempty"`
}

// ArchitectToolOutput 定义Architect工具的输出
type ArchitectToolOutput struct {
	Plan        string   `json:"plan"`
	Steps       []string `json:"steps"`
	Technologies []string `json:"technologies,omitempty"`
	Considerations []string `json:"considerations,omitempty"`
}

// ArchitectTool 架构师工具实现
type ArchitectTool struct {
	workspaceRoot string
}

// NewArchitectTool 创建新的架构师工具
func NewArchitectTool(workspaceRoot string) *ArchitectTool {
	return &ArchitectTool{
		workspaceRoot: workspaceRoot,
	}
}

// GetDefinition 获取工具定义
func (at *ArchitectTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "architect",
			Description: at.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The technical request or coding task to analyze",
					},
					"context": map[string]interface{}{
						"type":        "string",
						"description": "Optional context from previous conversation or system state",
					},
				},
				"required": []string{"prompt"},
			},
		},
	}
}

// Execute 执行架构师工具
func (at *ArchitectTool) Execute(arguments string) (string, error) {
	var input ArchitectToolInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("invalid arguments for architect function: %v", err)
	}

	// 验证输入
	if strings.TrimSpace(input.Prompt) == "" {
		return "", fmt.Errorf("prompt cannot be empty")
	}

	// 生成架构分析和计划
	output := at.analyzeAndPlan(input)
	
	// 格式化返回结果
	return at.formatResult(output), nil
}

// analyzeAndPlan 分析技术需求并生成实施计划
func (at *ArchitectTool) analyzeAndPlan(input ArchitectToolInput) *ArchitectToolOutput {
	prompt := strings.TrimSpace(input.Prompt)
	context := strings.TrimSpace(input.Context)
	
	// 构建完整的分析内容
	fullContent := prompt
	if context != "" {
		fullContent = fmt.Sprintf("Context: %s\n\nRequest: %s", context, prompt)
	}
	
	output := &ArchitectToolOutput{
		Plan: at.generatePlan(fullContent),
		Steps: at.generateSteps(fullContent),
		Technologies: at.suggestTechnologies(fullContent),
		Considerations: at.generateConsiderations(fullContent),
	}
	
	return output
}

// generatePlan 生成技术方案计划
func (at *ArchitectTool) generatePlan(content string) string {
	return fmt.Sprintf(`## Technical Analysis & Implementation Plan

Based on the requirements: "%s"

This appears to be a software development task that requires careful analysis of requirements and strategic implementation. The approach should follow software engineering best practices with clear separation of concerns and maintainable code structure.

### Core Approach:
1. **Requirements Analysis**: Break down the request into specific functional and non-functional requirements
2. **Technical Design**: Choose appropriate technologies, patterns, and architectures
3. **Implementation Strategy**: Plan the development approach with proper testing and validation
4. **Quality Assurance**: Ensure code quality, performance, and maintainability

The implementation should be modular, testable, and follow established patterns for the given technology stack.`, content)
}

// generateSteps 生成实施步骤
func (at *ArchitectTool) generateSteps(content string) []string {
	// 基于内容分析生成通用步骤
	steps := []string{
		"Analyze and clarify requirements thoroughly",
		"Research existing codebase patterns and conventions",
		"Design the solution architecture and data flow",
		"Identify required dependencies and libraries",
		"Create detailed implementation plan with milestones",
		"Implement core functionality with proper error handling",
		"Add comprehensive tests (unit, integration, e2e as needed)",
		"Perform code review and refactoring",
		"Document the implementation and usage",
		"Validate solution meets all requirements",
	}
	
	// 根据内容关键词调整步骤
	lowerContent := strings.ToLower(content)
	
	if strings.Contains(lowerContent, "api") || strings.Contains(lowerContent, "endpoint") {
		steps = append(steps[:5], append([]string{
			"Define API contracts and data schemas",
			"Implement request/response validation",
			"Add proper authentication and authorization",
		}, steps[5:]...)...)
	}
	
	if strings.Contains(lowerContent, "database") || strings.Contains(lowerContent, "db") {
		steps = append(steps[:4], append([]string{
			"Design database schema and relationships",
			"Plan data migration strategy",
		}, steps[4:]...)...)
	}
	
	if strings.Contains(lowerContent, "ui") || strings.Contains(lowerContent, "frontend") {
		steps = append(steps[:6], append([]string{
			"Create wireframes and component structure",
			"Implement responsive design patterns",
			"Add accessibility considerations",
		}, steps[6:]...)...)
	}
	
	return steps
}

// suggestTechnologies 建议技术栈
func (at *ArchitectTool) suggestTechnologies(content string) []string {
	technologies := []string{}
	lowerContent := strings.ToLower(content)
	
	// 基于内容关键词建议技术
	if strings.Contains(lowerContent, "go") || strings.Contains(lowerContent, "golang") {
		technologies = append(technologies, "Go", "Go modules", "Go testing framework")
	}
	
	if strings.Contains(lowerContent, "api") || strings.Contains(lowerContent, "rest") {
		technologies = append(technologies, "REST API", "JSON", "HTTP middleware")
	}
	
	if strings.Contains(lowerContent, "web") || strings.Contains(lowerContent, "http") {
		technologies = append(technologies, "HTTP server", "Router", "CORS middleware")
	}
	
	if strings.Contains(lowerContent, "database") || strings.Contains(lowerContent, "db") {
		technologies = append(technologies, "Database ORM", "Migration tools", "Connection pooling")
	}
	
	if strings.Contains(lowerContent, "test") {
		technologies = append(technologies, "Testing framework", "Mocking libraries", "Test fixtures")
	}
	
	if strings.Contains(lowerContent, "cli") || strings.Contains(lowerContent, "command") {
		technologies = append(technologies, "CLI framework", "Flag parsing", "Command routing")
	}
	
	// 默认通用技术建议
	if len(technologies) == 0 {
		technologies = []string{
			"Version control (Git)",
			"Package management",
			"Testing framework",
			"Logging library",
			"Configuration management",
		}
	}
	
	return technologies
}

// generateConsiderations 生成设计考虑事项
func (at *ArchitectTool) generateConsiderations(content string) []string {
	considerations := []string{
		"Code maintainability and readability",
		"Error handling and logging strategy",
		"Performance optimization opportunities",
		"Security best practices",
		"Scalability requirements",
		"Testing strategy and coverage",
		"Documentation and code comments",
		"Deployment and CI/CD pipeline",
	}
	
	lowerContent := strings.ToLower(content)
	
	// 根据内容添加特定考虑事项
	if strings.Contains(lowerContent, "api") {
		considerations = append(considerations, 
			"API versioning strategy",
			"Rate limiting and throttling",
			"Request validation and sanitization",
		)
	}
	
	if strings.Contains(lowerContent, "database") {
		considerations = append(considerations,
			"Data consistency and ACID properties",
			"Database indexing strategy",
			"Backup and recovery procedures",
		)
	}
	
	if strings.Contains(lowerContent, "concurrent") || strings.Contains(lowerContent, "parallel") {
		considerations = append(considerations,
			"Thread safety and race conditions",
			"Resource locking mechanisms",
			"Deadlock prevention",
		)
	}
	
	return considerations
}

// formatResult 格式化输出结果
func (at *ArchitectTool) formatResult(output *ArchitectToolOutput) string {
	result := output.Plan + "\n\n"
	
	result += "### Implementation Steps:\n"
	for i, step := range output.Steps {
		result += fmt.Sprintf("%d. %s\n", i+1, step)
	}
	
	if len(output.Technologies) > 0 {
		result += "\n### Recommended Technologies:\n"
		for _, tech := range output.Technologies {
			result += fmt.Sprintf("- %s\n", tech)
		}
	}
	
	if len(output.Considerations) > 0 {
		result += "\n### Key Considerations:\n"
		for _, consideration := range output.Considerations {
			result += fmt.Sprintf("- %s\n", consideration)
		}
	}
	
	result += "\n### Next Steps:\n"
	result += "1. Review and refine this plan based on specific project requirements\n"
	result += "2. Begin implementation starting with the core functionality\n"
	result += "3. Iterate and adapt the plan as development progresses\n"
	
	return result
}

// getDescription 获取工具描述
func (at *ArchitectTool) getDescription() string {
	return `Your go-to tool for any technical or coding task. Analyzes requirements and breaks them down into clear, actionable implementation steps. Use this whenever you need help planning how to implement a feature, solve a technical problem, or structure your code.

This tool will provide:
- Detailed technical analysis of the requirements
- Step-by-step implementation plan  
- Technology recommendations
- Important design considerations
- Best practices guidance

Perfect for planning new features, refactoring existing code, or solving complex technical challenges.`
}
