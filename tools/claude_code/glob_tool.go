package claude_code

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/sashabaranov/go-openai"
)

// GlobTool 文件模式匹配工具
type GlobTool struct {
	workspaceRoot string
}

// GlobInput glob工具输入参数
type GlobInput struct {
	Pattern string  `json:"pattern"`           // glob模式，如"**/*.js"
	Path    *string `json:"path,omitempty"`    // 搜索目录，可选，默认为工作目录
	Limit   *int    `json:"limit,omitempty"`   // 返回结果数量限制，默认100
	Offset  *int    `json:"offset,omitempty"`  // 结果偏移量，默认0
}

// GlobOutput glob工具输出结果
type GlobOutput struct {
	DurationMs int      `json:"durationMs"` // 执行时间（毫秒）
	NumFiles   int      `json:"numFiles"`   // 找到的文件数量
	Filenames  []string `json:"filenames"`  // 文件路径列表
	Truncated  bool     `json:"truncated"`  // 是否截断了结果
}

// GlobFileInfo 文件信息结构体，用于排序
type GlobFileInfo struct {
	Path    string
	ModTime time.Time
}

const (
	DefaultLimit = 100
	DefaultOffset = 0
)

// NewGlobTool 创建新的glob工具
func NewGlobTool(workspaceRoot string) *GlobTool {
	return &GlobTool{
		workspaceRoot: workspaceRoot,
	}
}

// GetDefinition 获取工具定义
func (t *GlobTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "glob_search", // 与 tools.go 中注册的名称保持一致
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"pattern": map[string]interface{}{
						"type":        "string",
						"description": "The glob pattern to match files against (e.g., '**/*.js', '*.go', 'src/**/*.ts')",
					},
					"path": map[string]interface{}{
						"type":        "string",
						"description": "The directory to search in. Defaults to the current working directory.",
					},
					"limit": map[string]interface{}{
						"type":        "integer",
						"description": "Maximum number of files to return. Defaults to 100.",
					},
					"offset": map[string]interface{}{
						"type":        "integer", 
						"description": "Number of files to skip from the beginning. Defaults to 0.",
					},
				},
				"required": []string{"pattern"},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *GlobTool) getDescription() string {
	return `Fast file pattern matching tool that works with any codebase size. Supports glob patterns like "**/*.js" or "src/**/*.ts". Returns matching file paths sorted by modification time. Use this tool when you need to find files by name patterns. When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead.`
}

// Execute 执行glob搜索
func (t *GlobTool) Execute(arguments string) (string, error) {
	start := time.Now()
	
	var input GlobInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 设置默认值
	searchPath := t.workspaceRoot
	if input.Path != nil && *input.Path != "" {
		if filepath.IsAbs(*input.Path) {
			searchPath = *input.Path
		} else {
			searchPath = filepath.Join(t.workspaceRoot, *input.Path)
		}
	}

	limit := DefaultLimit
	if input.Limit != nil {
		limit = *input.Limit
	}

	offset := DefaultOffset
	if input.Offset != nil {
		offset = *input.Offset
	}

	// 检查搜索路径是否存在
	if _, err := os.Stat(searchPath); os.IsNotExist(err) {
		return "", fmt.Errorf("search path does not exist: %s", searchPath)
	}

	// 执行glob搜索
	files, err := t.globFiles(input.Pattern, searchPath)
	if err != nil {
		return "", fmt.Errorf("glob search failed: %v", err)
	}

	// 按修改时间排序（最新的在前）
	sort.Slice(files, func(i, j int) bool {
		return files[i].ModTime.After(files[j].ModTime)
	})

	// 应用分页
	totalFiles := len(files)
	truncated := totalFiles > offset+limit
	
	endIndex := offset + limit
	if endIndex > totalFiles {
		endIndex = totalFiles
	}
	
	startIndex := offset
	if startIndex > totalFiles {
		startIndex = totalFiles
	}

	var resultFiles []string
	if startIndex < endIndex {
		for i := startIndex; i < endIndex; i++ {
			// 返回相对于工作目录的路径
			relPath, err := filepath.Rel(t.workspaceRoot, files[i].Path)
			if err != nil {
				// 如果无法获取相对路径，使用绝对路径
				resultFiles = append(resultFiles, files[i].Path)
			} else {
				resultFiles = append(resultFiles, relPath)
			}
		}
	}

	// 构建输出结果
	output := GlobOutput{
		DurationMs: int(time.Since(start).Milliseconds()),
		NumFiles:   len(resultFiles),
		Filenames:  resultFiles,
		Truncated:  truncated,
	}

	// 序列化结果
	resultJSON, err := json.Marshal(output)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// globFiles 执行glob搜索并收集文件信息
func (t *GlobTool) globFiles(pattern, searchPath string) ([]GlobFileInfo, error) {
	var files []GlobFileInfo

	// 处理不同类型的glob模式
	if strings.Contains(pattern, "**") {
		// 递归搜索模式
		err := t.walkGlob(searchPath, pattern, &files)
		if err != nil {
			return nil, err
		}
	} else {
		// 简单glob模式
		fullPattern := filepath.Join(searchPath, pattern)
		matches, err := filepath.Glob(fullPattern)
		if err != nil {
			return nil, err
		}

		for _, match := range matches {
			info, err := os.Stat(match)
			if err != nil {
				continue // 跳过无法访问的文件
			}
			if !info.IsDir() {
				files = append(files, GlobFileInfo{
					Path:    match,
					ModTime: info.ModTime(),
				})
			}
		}
	}

	return files, nil
}

// walkGlob 处理递归glob搜索（支持**模式）
func (t *GlobTool) walkGlob(searchPath, pattern string, files *[]GlobFileInfo) error {
	// 将glob模式转换为filepath.Walk可以处理的形式
	return filepath.Walk(searchPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 跳过错误的文件/目录
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件是否匹配模式
		if t.matchesGlobPattern(path, searchPath, pattern) {
			*files = append(*files, GlobFileInfo{
				Path:    path,
				ModTime: info.ModTime(),
			})
		}

		return nil
	})
}

// matchesGlobPattern 检查文件路径是否匹配glob模式
func (t *GlobTool) matchesGlobPattern(filePath, searchPath, pattern string) bool {
	// 获取相对于搜索路径的相对路径
	relPath, err := filepath.Rel(searchPath, filePath)
	if err != nil {
		return false
	}

	// 将Windows路径分隔符转换为Unix风格（glob模式通常使用/）
	relPath = filepath.ToSlash(relPath)
	pattern = filepath.ToSlash(pattern)

	// 处理**模式
	if strings.Contains(pattern, "**") {
		return t.matchesRecursivePattern(relPath, pattern)
	}

	// 使用filepath.Match进行简单模式匹配
	matched, err := filepath.Match(pattern, relPath)
	if err != nil {
		return false
	}

	return matched
}

// matchesRecursivePattern 处理包含**的递归模式匹配
func (t *GlobTool) matchesRecursivePattern(filePath, pattern string) bool {
	// 将**替换为通配符进行匹配
	parts := strings.Split(pattern, "**")
	if len(parts) != 2 {
		// 简化处理，只支持一个**
		return false
	}

	prefix := parts[0]
	suffix := parts[1]

	// 移除前缀的尾部斜杠
	if prefix != "" && strings.HasSuffix(prefix, "/") {
		prefix = prefix[:len(prefix)-1]
	}

	// 移除后缀的开头斜杠
	if suffix != "" && strings.HasPrefix(suffix, "/") {
		suffix = suffix[1:]
	}

	// 检查前缀匹配
	if prefix != "" && !strings.HasPrefix(filePath, prefix) {
		return false
	}

	// 检查后缀匹配
	if suffix != "" {
		// 提取文件名进行后缀匹配
		fileName := filepath.Base(filePath)
		matched, err := filepath.Match(suffix, fileName)
		if err != nil {
			return false
		}
		return matched
	}

	return true
}
