package claude_code

import (
	"fmt"
	"log"

	"github.com/sasha<PERSON><PERSON>/go-openai"
	"codefuse-cli/tools"
)

// ClaudeCodeToolManager Claude Code工具管理器
type ClaudeCodeToolManager struct {
	tools map[string]tools.Tool
}

// NewClaudeCodeToolManager 创建新的Claude Code工具管理器
func NewClaudeCodeToolManager() *ClaudeCodeToolManager {
	return &ClaudeCodeToolManager{
		tools: make(map[string]tools.Tool),
	}
}

// RegisterTool 注册工具
func (tm *ClaudeCodeToolManager) RegisterTool(name string, tool tools.Tool) {
	tm.tools[name] = tool
}

// GetTools 获取所有工具定义
func (tm *ClaudeCodeToolManager) GetTools() []openai.Tool {
	var toolList []openai.Tool
	for _, tool := range tm.tools {
		toolList = append(toolList, tool.GetDefinition())
	}
	return toolList
}

// GetToolNames 获取所有已注册的工具名称
func (tm *<PERSON><PERSON>odeToolManager) GetToolNames() []string {
	var names []string
	for name := range tm.tools {
		names = append(names, name)
	}
	return names
}

// HasTool 检查是否有指定工具
func (tm *ClaudeCodeToolManager) HasTool(name string) bool {
	_, exists := tm.tools[name]
	return exists
}

// ExecuteTool 执行工具
func (tm *ClaudeCodeToolManager) ExecuteTool(name string, arguments string) (string, error) {
	tool, exists := tm.tools[name]
	if !exists {
		return "", fmt.Errorf("unknown tool: %s", name)
	}
	return tool.Execute(arguments)
}

// registerAllTools 注册所有Claude Code工具
func (tm *ClaudeCodeToolManager) registerAllTools(config *tools.ProviderConfig) {
	log.Printf("📋 Registering Claude Code tools...")
	
	// 注册bash工具
	tm.RegisterTool("bash", NewBashTool(config.WorkspaceRoot))
	log.Printf("✅ bash tool registered")
	
	// 注册architect工具
	tm.RegisterTool("architect", NewArchitectTool(config.WorkspaceRoot))
	log.Printf("✅ architect tool registered")
	
	// 注册grep搜索工具
	tm.RegisterTool("grep_search", NewGrepTool(config.WorkspaceRoot))
	log.Printf("✅ grep_search tool registered")
	
	// 注册glob文件搜索工具
	tm.RegisterTool("glob_search", NewGlobTool(config.WorkspaceRoot))
	log.Printf("✅ glob_search tool registered")
	
	// 注册ls工具
	tm.RegisterTool("ls", NewLsTool(config.WorkspaceRoot))
	log.Printf("✅ ls tool registered")
	
	// 注册文件编辑、读取和写入工具，并设置交叉引用
	fileEditTool := NewFileEditTool(config.WorkspaceRoot)
	fileReadTool := NewFileReadTool(config.WorkspaceRoot)
	fileWriteTool := NewFileWriteTool(config.WorkspaceRoot)
	
	// 设置交叉引用以共享时间戳
	fileReadTool.SetFileEditTool(fileEditTool)
	fileWriteTool.SetFileReadTool(fileReadTool)
	
	tm.RegisterTool("file_edit", fileEditTool)
	log.Printf("✅ file_edit tool registered")
	
	tm.RegisterTool("file_read", fileReadTool)
	log.Printf("✅ file_read tool registered")
	
	tm.RegisterTool("file_write", fileWriteTool)
	log.Printf("✅ file_write tool registered")
	
	// 注册think工具
	tm.RegisterTool("think", NewThinkTool(config.WorkspaceRoot))
	log.Printf("✅ think tool registered")
	
	// 注册内存读取工具
	tm.RegisterTool("memory_read", NewMemoryReadTool(config.WorkspaceRoot))
	log.Printf("✅ memory_read tool registered")
	
	// 注册内存写入工具
	tm.RegisterTool("memory_write", NewMemoryWriteTool(config.WorkspaceRoot))
	log.Printf("✅ memory_write tool registered")
	
	// 可以根据需要添加更多Claude Code特有的工具
	// tm.RegisterTool("claude_search", NewClaudeSearchTool(config.WorkspaceRoot))
}

// ClaudeCodeProvider Claude Code算法提供者
type ClaudeCodeProvider struct{}

// GetName 获取Provider名称
func (cp *ClaudeCodeProvider) GetName() string {
	return "claude_code"
}

// GetDescription 获取Provider描述
func (cp *ClaudeCodeProvider) GetDescription() string {
	return "Claude Code Provider - Code editing and analysis tools inspired by Claude"
}

// CreateToolManager 创建ToolManager
func (cp *ClaudeCodeProvider) CreateToolManager(config *tools.ProviderConfig) tools.ToolManager {
	tm := NewClaudeCodeToolManager()
	tm.registerAllTools(config)
	return tm
}

// CreatePromptManager 创建PromptManager
func (cp *ClaudeCodeProvider) CreatePromptManager(config *tools.ProviderConfig) tools.PromptManager {
	return NewPromptManager(config.WorkspaceRoot, config.GitInfo)
} 