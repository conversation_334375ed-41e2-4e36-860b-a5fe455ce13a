package claude_code

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/sasha<PERSON>nov/go-openai"
)

// FileWriteTool 文件写入工具
type FileWriteTool struct {
	workspaceRoot      string
	readTimestamps     map[string]time.Time // 文件读取时间戳映射，与FileReadTool共享
	fileReadTool       *FileReadTool        // 引用FileReadTool来共享时间戳
}

// FileWriteInput 文件写入工具输入参数
type FileWriteInput struct {
	FilePath string `json:"file_path"` // 要写入的绝对路径
	Content  string `json:"content"`   // 文件内容
}

// FileWriteResult 文件写入结果
type FileWriteResult struct {
	Type         string `json:"type"`          // "create" 或 "update"
	FilePath     string `json:"file_path"`     // 文件路径
	Content      string `json:"content"`       // 文件内容
	NumLines     int    `json:"num_lines"`     // 行数
	Message      string `json:"message"`       // 结果消息
	Success      bool   `json:"success"`       // 是否成功
}

const (
	MaxLinesForPreview = 50 // 预览显示的最大行数
)

// NewFileWriteTool 创建文件写入工具
func NewFileWriteTool(workspaceRoot string) *FileWriteTool {
	return &FileWriteTool{
		workspaceRoot:  workspaceRoot,
		readTimestamps: make(map[string]time.Time),
	}
}

// SetFileReadTool 设置FileReadTool引用以共享时间戳
func (t *FileWriteTool) SetFileReadTool(fileReadTool *FileReadTool) {
	t.fileReadTool = fileReadTool
	// 共享时间戳映射
	t.readTimestamps = fileReadTool.readTimestamps
}

// GetDefinition 获取工具定义
func (t *FileWriteTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "file_write",
			Description: t.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "The absolute path to the file to write (must be absolute, not relative)",
					},
					"content": map[string]interface{}{
						"type":        "string",
						"description": "The content to write to the file",
					},
				},
				"required": []string{"file_path", "content"},
			},
		},
	}
}

// getDescription 获取工具描述
func (t *FileWriteTool) getDescription() string {
	return `Write a file to the local filesystem. Overwrites the existing file if there is one.

Before using this tool:

1. Use the file_read tool to understand the file's contents and context

2. Directory Verification (only applicable when creating new files):
   - Use the ls tool to verify the parent directory exists and is the correct location

The tool will:
- Create parent directories if they don't exist
- Preserve file encoding when updating existing files
- Detect and preserve line endings (LF vs CRLF)
- Validate file timestamps to prevent race conditions
- Generate diffs for existing file updates`
}

// Execute 执行文件写入
func (t *FileWriteTool) Execute(arguments string) (string, error) {
	var input FileWriteInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("failed to parse arguments: %v", err)
	}

	// 验证输入
	if validationErr := t.validateInput(input); validationErr != nil {
		result := FileWriteResult{
			FilePath: input.FilePath,
			Success:  false,
			Message:  validationErr.Error(),
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON), nil
	}

	// 执行写入
	result, err := t.writeFile(input)
	if err != nil {
		result = FileWriteResult{
			FilePath: input.FilePath,
			Success:  false,
			Message:  err.Error(),
		}
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %v", err)
	}

	return string(resultJSON), nil
}

// validateInput 验证输入参数
func (t *FileWriteTool) validateInput(input FileWriteInput) error {
	// 转换为绝对路径
	fullFilePath := input.FilePath
	if !filepath.IsAbs(fullFilePath) {
		return fmt.Errorf("file_path must be absolute, not relative: %s", input.FilePath)
	}

	// 检查文件是否存在
	fileExists := true
	fileInfo, err := os.Stat(fullFilePath)
	if os.IsNotExist(err) {
		fileExists = false
	} else if err != nil {
		return fmt.Errorf("failed to check file status: %v", err)
	}

	// 如果文件存在，检查时间戳
	if fileExists {
		// 检查是否已读取过该文件
		readTimestamp, hasReadTimestamp := t.getReadTimestamp(fullFilePath)
		if !hasReadTimestamp {
			return fmt.Errorf("file has not been read yet. Read it first before writing to it")
		}

		// 检查文件是否在读取后被修改
		lastWriteTime := fileInfo.ModTime()
		if lastWriteTime.After(readTimestamp) {
			return fmt.Errorf("file has been modified since read, either by the user or by a linter. Read it again before attempting to write it")
		}
	}

	// 验证内容是否为有效UTF-8
	if !utf8.ValidString(input.Content) {
		return fmt.Errorf("content contains invalid UTF-8 characters")
	}

	return nil
}

// writeFile 执行文件写入
func (t *FileWriteTool) writeFile(input FileWriteInput) (FileWriteResult, error) {
	fullFilePath := input.FilePath
	
	// 检查文件是否存在
	var oldContent string
	var writeType string
	
	if _, err := os.Stat(fullFilePath); os.IsNotExist(err) {
		writeType = "create"
	} else {
		writeType = "update"
		// 读取原有内容用于生成diff
		oldContentBytes, err := os.ReadFile(fullFilePath)
		if err != nil {
			return FileWriteResult{}, fmt.Errorf("failed to read existing file: %v", err)
		}
		oldContent = string(oldContentBytes)
	}

	// 创建目录（如果不存在）
	dir := filepath.Dir(fullFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return FileWriteResult{}, fmt.Errorf("failed to create directory %s: %v", dir, err)
	}

	// 检测行结束符
	lineEnding := t.detectLineEndings(oldContent, fullFilePath)
	
	// 标准化内容的行结束符
	content := t.normalizeLineEndings(input.Content, lineEnding)

	// 写入文件
	if err := os.WriteFile(fullFilePath, []byte(content), 0644); err != nil {
		return FileWriteResult{}, fmt.Errorf("failed to write file: %v", err)
	}

	// 更新读取时间戳
	t.updateReadTimestamp(fullFilePath)

	// 计算行数
	lines := strings.Split(content, "\n")
	numLines := len(lines)
	if lines[len(lines)-1] == "" {
		numLines--
	}

	// 生成结果消息
	var message string
	if writeType == "create" {
		message = fmt.Sprintf("Successfully created file with %d lines", numLines)
	} else {
		message = fmt.Sprintf("Successfully updated file with %d lines", numLines)
		// 可以在这里生成diff信息
		if oldContent != content {
			diffSummary := t.generateDiffSummary(oldContent, content)
			message += fmt.Sprintf("\n%s", diffSummary)
		}
	}

	return FileWriteResult{
		Type:     writeType,
		FilePath: fullFilePath,
		Content:  content,
		NumLines: numLines,
		Message:  message,
		Success:  true,
	}, nil
}

// detectLineEndings 检测行结束符
func (t *FileWriteTool) detectLineEndings(content, filePath string) string {
	if content == "" {
		// 对于新文件，使用系统默认的行结束符
		// 在Unix/Linux/macOS上使用LF，在Windows上使用CRLF
		if strings.Contains(strings.ToLower(filepath.Ext(filePath)), "bat") ||
		   strings.Contains(strings.ToLower(filepath.Ext(filePath)), "cmd") {
			return "\r\n" // Windows批处理文件
		}
		return "\n" // 默认使用LF
	}

	// 检测现有内容的行结束符
	if strings.Contains(content, "\r\n") {
		return "\r\n"
	}
	return "\n"
}

// normalizeLineEndings 标准化行结束符
func (t *FileWriteTool) normalizeLineEndings(content, lineEnding string) string {
	// 首先将所有行结束符标准化为\n
	content = strings.ReplaceAll(content, "\r\n", "\n")
	content = strings.ReplaceAll(content, "\r", "\n")
	
	// 然后转换为目标行结束符
	if lineEnding == "\r\n" {
		content = strings.ReplaceAll(content, "\n", "\r\n")
	}
	
	return content
}

// generateDiffSummary 生成diff摘要
func (t *FileWriteTool) generateDiffSummary(oldContent, newContent string) string {
	oldLines := strings.Split(oldContent, "\n")
	newLines := strings.Split(newContent, "\n")
	
	oldLen := len(oldLines)
	newLen := len(newLines)
	
	if oldLen == 0 && newLen > 0 {
		return fmt.Sprintf("Added %d lines", newLen)
	} else if oldLen > 0 && newLen == 0 {
		return fmt.Sprintf("Removed %d lines", oldLen)
	} else {
		diff := newLen - oldLen
		if diff > 0 {
			return fmt.Sprintf("Modified file: +%d lines (total: %d lines)", diff, newLen)
		} else if diff < 0 {
			return fmt.Sprintf("Modified file: %d lines (total: %d lines)", diff, newLen)
		} else {
			return fmt.Sprintf("Modified file: same number of lines (%d lines)", newLen)
		}
	}
}

// getReadTimestamp 获取文件读取时间戳
func (t *FileWriteTool) getReadTimestamp(filePath string) (time.Time, bool) {
	if t.fileReadTool != nil {
		return t.fileReadTool.GetReadTimestamp(filePath)
	}
	timestamp, exists := t.readTimestamps[filePath]
	return timestamp, exists
}

// updateReadTimestamp 更新文件读取时间戳
func (t *FileWriteTool) updateReadTimestamp(filePath string) {
	now := time.Now()
	if t.fileReadTool != nil {
		// 如果有FileReadTool引用，更新其时间戳
		t.fileReadTool.readTimestamps[filePath] = now
	}
	t.readTimestamps[filePath] = now
}

// SetReadTimestamp 设置读取时间戳（供外部调用）
func (t *FileWriteTool) SetReadTimestamp(filePath string, timestamp time.Time) {
	if t.fileReadTool != nil {
		t.fileReadTool.readTimestamps[filePath] = timestamp
	}
	t.readTimestamps[filePath] = timestamp
}

// GetReadTimestamp 获取读取时间戳（供外部调用）  
func (t *FileWriteTool) GetReadTimestamp(filePath string) (time.Time, bool) {
	return t.getReadTimestamp(filePath)
}
