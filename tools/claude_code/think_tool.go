package claude_code

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
)

// ThinkToolInput 定义think工具的输入参数
type ThinkToolInput struct {
	Thought string `json:"thought"`
}

// ThinkTool think工具实现
type ThinkTool struct {
	workspaceRoot string
}

// NewThinkTool 创建新的think工具
func NewThinkTool(workspaceRoot string) *ThinkTool {
	return &ThinkTool{
		workspaceRoot: workspaceRoot,
	}
}

// GetDefinition 获取工具定义
func (tt *ThinkTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "think",
			Description: tt.getDescription(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"thought": map[string]interface{}{
						"type":        "string",
						"description": "Your thoughts.",
					},
				},
				"required": []string{"thought"},
			},
		},
	}
}

// Execute 执行think命令
func (tt *ThinkTool) Execute(arguments string) (string, error) {
	var input ThinkToolInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("invalid arguments for think function: %v", err)
	}

	// 记录思考内容到日志
	log.Printf("💭 Think Tool - Thought logged (length: %d chars)", len(input.Thought))

	// 可以选择记录思考内容的详细信息（在开发/调试模式下）
	if len(input.Thought) > 0 {
		log.Printf("💭 Thought content: %s", input.Thought)
	}

	// 返回固定的确认消息
	return "Your thought has been logged.", nil
}

// getDescription 获取工具描述
func (tt *ThinkTool) getDescription() string {
	return "This is a no-op tool that logs a thought. It is inspired by the tau-bench think tool."
}

// getPrompt 获取工具提示（如果需要更详细的说明）
func (tt *ThinkTool) getPrompt() string {
	return `Use the tool to think about something. It will not obtain new information or make any changes to the repository, but just log the thought. Use it when complex reasoning or brainstorming is needed.

Common use cases:
1. When exploring a repository and discovering the source of a bug, call this tool to brainstorm several unique ways of fixing the bug, and assess which change(s) are likely to be simplest and most effective
2. After receiving test results, use this tool to brainstorm ways to fix failing tests
3. When planning a complex refactoring, use this tool to outline different approaches and their tradeoffs
4. When designing a new feature, use this tool to think through architecture decisions and implementation details
5. When debugging a complex issue, use this tool to organize your thoughts and hypotheses

The tool simply logs your thought process for better transparency and does not execute any code or make changes.`
}
