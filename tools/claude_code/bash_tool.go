package claude_code

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"syscall"
	"time"

	"github.com/sashabaranov/go-openai"
)

// 常量定义
const (
	MaxOutputLength   = 30000
	MaxRenderedLines  = 50
	DefaultTimeoutMs  = 120000 // 2分钟默认超时
	MaxTimeoutMs      = 600000 // 10分钟最大超时
)

// 禁用的命令列表
var BannedCommands = []string{
	"alias", "curl", "curlie", "wget", "axel", "aria2c", "nc", "telnet",
	"lynx", "w3m", "links", "httpie", "xh", "http-prompt", "chrome",
	"firefox", "safari",
}

// BashToolInput 定义bash工具的输入参数
type BashToolInput struct {
	Command string `json:"command"`
	Timeout *int   `json:"timeout,omitempty"`
}

// BashToolOutput 定义bash工具的输出
type BashToolOutput struct {
	Stdout       string `json:"stdout"`
	StdoutLines  int    `json:"stdout_lines"`
	Stderr       string `json:"stderr"`
	StderrLines  int    `json:"stderr_lines"`
	Interrupted  bool   `json:"interrupted"`
	ExitCode     int    `json:"exit_code"`
}

// FormattedOutput 格式化后的输出
type FormattedOutput struct {
	Content    string
	TotalLines int
}

// formatOutput 格式化输出内容
func formatOutput(content string) FormattedOutput {
	lines := strings.Split(content, "\n")
	totalLines := len(lines)

	if len(content) <= MaxOutputLength {
		return FormattedOutput{
			Content:    content,
			TotalLines: totalLines,
		}
	}

	// 截断输出
	halfLength := MaxOutputLength / 2
	if len(content) > MaxOutputLength {
		start := content[:halfLength]
		end := content[len(content)-halfLength:]
		
		// 计算被截断的行数
		middlePart := content[halfLength : len(content)-halfLength]
		truncatedLines := len(strings.Split(middlePart, "\n"))
		
		truncated := fmt.Sprintf("%s\n\n... [%d lines truncated] ...\n\n%s", 
			start, truncatedLines, end)
		
		return FormattedOutput{
			Content:    truncated,
			TotalLines: totalLines,
		}
	}

	return FormattedOutput{
		Content:    content,
		TotalLines: totalLines,
	}
}

// BashTool bash工具实现
type BashTool struct {
	workspaceRoot string
}

// NewBashTool 创建新的bash工具
func NewBashTool(workspaceRoot string) *BashTool {
	return &BashTool{
		workspaceRoot: workspaceRoot,
	}
}

// GetDefinition 获取工具定义
func (bt *BashTool) GetDefinition() openai.Tool {
	return openai.Tool{
		Type: openai.ToolTypeFunction,
		Function: &openai.FunctionDefinition{
			Name:        "bash",
			Description: bt.getPrompt(),
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"command": map[string]interface{}{
						"type":        "string",
						"description": "The command to execute",
					},
					"timeout": map[string]interface{}{
						"type":        "integer",
						"description": "Optional timeout in milliseconds (max 600000)",
					},
				},
				"required": []string{"command"},
			},
		},
	}
}

// Execute 执行bash命令
func (bt *BashTool) Execute(arguments string) (string, error) {
	var input BashToolInput
	if err := json.Unmarshal([]byte(arguments), &input); err != nil {
		return "", fmt.Errorf("invalid arguments for bash function: %v", err)
	}

	// 验证输入
	if err := bt.validateInput(input); err != nil {
		return "", err
	}

	// 设置超时
	timeout := time.Duration(DefaultTimeoutMs) * time.Millisecond
	if input.Timeout != nil {
		if *input.Timeout > MaxTimeoutMs {
			return "", fmt.Errorf("timeout cannot exceed %d milliseconds", MaxTimeoutMs)
		}
		timeout = time.Duration(*input.Timeout) * time.Millisecond
	}

	// 执行命令
	output, err := bt.execCommand(input.Command, timeout)
	if err != nil {
		return "", fmt.Errorf("failed to execute command: %v", err)
	}

	// 格式化返回结果
	return bt.formatResult(output), nil
}

// execCommand 执行单个命令
func (bt *BashTool) execCommand(command string, timeout time.Duration) (*BashToolOutput, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建命令，设置工作目录为 workspace root
	cmd := exec.CommandContext(ctx, "bash", "-c", command)
	cmd.Dir = bt.workspaceRoot

	// 使用 CombinedOutput 简化输出处理
	output, err := cmd.CombinedOutput()
	
	// 检查是否被中断
	interrupted := false
	if ctx.Err() == context.DeadlineExceeded {
		interrupted = true
	}

	// 获取退出码
	exitCode := 0
	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			if status, ok := exitError.Sys().(syscall.WaitStatus); ok {
				exitCode = status.ExitStatus()
			}
		}
	}

	// 将输出作为 stdout 处理
	stdoutStr := strings.TrimSpace(string(output))
	stderrStr := ""

	// 添加退出码信息到stderr（如果有错误）
	if exitCode != 0 && !interrupted {
		stderrStr = fmt.Sprintf("Exit code %d", exitCode)
	}

	// 格式化输出
	formattedStdout := formatOutput(stdoutStr)
	formattedStderr := formatOutput(stderrStr)

	return &BashToolOutput{
		Stdout:      formattedStdout.Content,
		StdoutLines: formattedStdout.TotalLines,
		Stderr:      formattedStderr.Content,
		StderrLines: formattedStderr.TotalLines,
		Interrupted: interrupted,
		ExitCode:    exitCode,
	}, nil
}

// validateInput 验证输入参数
func (bt *BashTool) validateInput(input BashToolInput) error {
	if input.Command == "" {
		return fmt.Errorf("command cannot be empty")
	}

	// 检查禁用命令
	commands := bt.splitCommand(input.Command)
	for _, cmd := range commands {
		parts := strings.Fields(cmd)
		if len(parts) > 0 {
			baseCmd := strings.ToLower(parts[0])
			for _, banned := range BannedCommands {
				if baseCmd == banned {
					return fmt.Errorf("command '%s' is not allowed for security reasons", baseCmd)
				}
			}
		}

		// 特殊处理cd命令
		if len(parts) >= 2 && parts[0] == "cd" {
			targetDir := strings.Trim(parts[1], "'\"")
			if err := bt.validateCdCommand(targetDir); err != nil {
				return err
			}
		}
	}

	return nil
}

// splitCommand 分割命令
func (bt *BashTool) splitCommand(command string) []string {
	// 简单的命令分割，按分号和&&分割
	re := regexp.MustCompile(`[;&]+`)
	return re.Split(command, -1)
}

// validateCdCommand 验证cd命令
func (bt *BashTool) validateCdCommand(targetDir string) error {
	var fullTargetDir string
	if filepath.IsAbs(targetDir) {
		fullTargetDir = targetDir
	} else {
		fullTargetDir = filepath.Join(bt.workspaceRoot, targetDir)
	}

	// 检查是否在工作目录内
	rel, err := filepath.Rel(bt.workspaceRoot, fullTargetDir)
	if err != nil {
		return fmt.Errorf("failed to resolve path: %v", err)
	}

	if strings.HasPrefix(rel, "..") {
		return fmt.Errorf("ERROR: cd to '%s' was blocked. For security, CodeFuse CLI may only change directories to child directories of the workspace root (%s) for this session", fullTargetDir, bt.workspaceRoot)
	}

	return nil
}

// formatResult 格式化返回结果
func (bt *BashTool) formatResult(output *BashToolOutput) string {
	var result strings.Builder

	// 添加stdout
	if output.Stdout != "" {
		result.WriteString(output.Stdout)
	}

	// 添加stderr
	if output.Stderr != "" {
		if output.Stdout != "" {
			result.WriteString("\n")
		}
		result.WriteString(output.Stderr)
	}

	// 如果被中断，添加中断信息
	if output.Interrupted {
		if result.Len() > 0 {
			result.WriteString("\n")
		}
		result.WriteString("<error>Command was aborted before completion</error>")
	}

	return result.String()
}

// getPrompt 获取工具的详细说明
func (bt *BashTool) getPrompt() string {
	return `Executes a given bash command with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Security Check:
   - For security and to limit the threat of a prompt injection attack, some commands are limited or banned. If you use a disallowed command, you will receive an error message explaining the restriction.
   - Verify that the command is not one of the banned commands: ` + strings.Join(BannedCommands, ", ") + `.

2. Command Execution:
   - Execute the command in the workspace root directory.
   - Capture the output of the command.

3. Output Processing:
   - If the output exceeds ` + fmt.Sprintf("%d", MaxOutputLength) + ` characters, output will be truncated before being returned to you.
   - Prepare the output for display to the user.

4. Return Result:
   - Provide the processed output of the command.
   - If any errors occurred during execution, include those in the output.

Usage notes:
  - The command argument is required.
  - You can specify an optional timeout in milliseconds (up to ` + fmt.Sprintf("%d", MaxTimeoutMs) + `ms / 10 minutes). If not specified, commands will timeout after 2 minutes.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).
  - Each command execution is independent - no shell state persists between commands.
  
Important notes:
- NEVER update the git config
- DO NOT push to the remote repository  
- IMPORTANT: Never use git commands with the -i flag (like git rebase -i or git add -i) since they require interactive input which is not supported.
- If there are no changes to commit (i.e., no untracked files and no modifications), do not create an empty commit
- Ensure your commit message is meaningful and concise. It should explain the purpose of the changes, not just describe them.`
}
